<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('slugs', function (Blueprint $table) {
            $table->id();
            $table->string('slug');
            $table->string('locale', 2);
            $table->unsignedBigInteger('sluggable_id');
            $table->string('sluggable_type');
            $table->timestamps();

            $table->unique(['slug', 'locale']);
            $table->index(['slug', 'locale']);
            $table->index(['sluggable_id', 'sluggable_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('slugs');
    }
};
