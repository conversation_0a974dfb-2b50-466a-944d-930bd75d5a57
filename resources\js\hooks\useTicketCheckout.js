import { useCallback } from "react";
import { router } from "@inertiajs/react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchReservation,
    setAttendees,
    setIsAgreed,
    setShowTimesUpPopup,
    setStep,
} from "@/redux/slices/TicketCheckoutSlice";

export default function useTicketCheckout() {
    const {
        reservation,
        reservationLoading,
        attendees,
        showTimesUpPopup,
        step,
        isAgreed,
    } = useSelector((state) => state.ticketCheckout);
    const dispatch = useDispatch();

    const getReservation = (reservationId) => {
        dispatch(
            fetchReservation({
                url: route("api.reservations.detail", { reservationId }),
            }),
        );
    };

    const updateAttendees = (index, field, value) => {
        dispatch(setAttendees({ index, field, value }));
    };

    const updateIsAgreed = (key, value) => {
        dispatch(setIsAgreed({ key, value }));
    };

    const handleTimesUpPopup = (value) => {
        dispatch(setShowTimesUpPopup({ value }));
    };

    const nextStep = () => {
        dispatch(setStep({ value: Math.min(step + 1, 4) }));
    };

    const prevStep = () => {
        dispatch(setStep({ value: Math.max(step - 1, 1) }));
    };

    const handleTimesUpPopupCloseClick = () => {
        router.visit(
            route("detail.show", reservation.ticket.event.localized_slug.slug),
        );
    };

    return {
        reservation,
        reservationLoading,
        getReservation,
        attendees,
        isAgreed,
        showTimesUpPopup,
        updateAttendees,
        updateIsAgreed,
        handleTimesUpPopup,
        handleTimesUpPopupCloseClick,
        step,
        nextStep,
        prevStep,
    };
}
