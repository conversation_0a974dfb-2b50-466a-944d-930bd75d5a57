<?php

namespace Database\Factories;

use App\Models\Country;
use App\Models\Language;
use App\Models\Stadium;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Club>
 */
class ClubFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $country = Country::whereIn('id', Stadium::select('country_id')->distinct())->inRandomOrder()->first();

        return [
            'country_id' => $country->id,
            'stadium_id' => Stadium::where('country_id', $country->id)->inRandomOrder()->first(),
            'is_active' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($club) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $slugs = [];
            $translationData = [];

            foreach ($languages as $value) {
                $name = $this->faker->company;
                $translationData[] = [
                    'club_id' => $club->id,
                    'locale' => $value,
                    'name' => $name,
                    'description' => $this->faker->paragraph,
                    'detailed_description' => $this->faker->paragraph,
                    'meta_title' => $this->faker->sentence,
                    'meta_description' => $this->faker->sentence,
                    'meta_keywords' => $this->faker->sentence,
                ];

                $slugs[$value] = Str::slug('club-'.$name.'-'.$club->id);
            }

            $club->translations()->createMany($translationData);

            $club->setSlugs($slugs);
        });
    }
}
