{"__meta": {"id": "Xe6d0a84b32acaa1c323090301f955b83", "datetime": "2025-06-10 07:38:25", "utime": 1749541105.005305, "method": "GET", "uri": "/api/v1/orders/statuses", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749541104.882645, "end": 1749541105.00532, "duration": 0.12267518043518066, "duration_str": "123ms", "measures": [{"label": "Booting", "start": 1749541104.882645, "relative_start": 0, "end": 1749541104.974105, "relative_end": 1749541104.974105, "duration": 0.09145998954772949, "duration_str": "91.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749541104.974116, "relative_start": 0.09147119522094727, "end": 1749541105.005325, "relative_end": 5.0067901611328125e-06, "duration": 0.03120899200439453, "duration_str": "31.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5237832, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders/statuses", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@getStatuses", "namespace": null, "where": [], "as": "api.orders.statuses", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=29\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:29-34</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00995, "accumulated_duration_str": "9.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": 1749541104.9868002, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 6.231}, {"sql": "select * from `users` where `users`.`id` = 5 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": 1749541104.989204, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "ticketgol", "explain": null, "start_percent": 6.231, "width_percent": 6.03}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-10 07:38:24', `personal_access_tokens`.`updated_at` = '2025-06-10 07:38:24' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-06-10 07:38:24", "2025-06-10 07:38:24", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749541104.992386, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "ticketgol", "explain": null, "start_percent": 12.261, "width_percent": 87.739}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f1ead9b-bf1c-470e-984b-9a77f6cc1eb2\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders/statuses", "status_code": "<pre class=sf-dump id=sf-dump-1573813607 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1573813607\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1922250021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922250021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-846933532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-846933532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1346452405 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"735 characters\">XSRF-TOKEN=eyJpdiI6Ik5vMzlLcm9rZ0F4dDRSZmpsWVpoanc9PSIsInZhbHVlIjoieVNxelJ4T0dpVzhDdXYvOWYvdXF1N3gwTlFIR2FUUENqZGdPWXlDaVdTbXpTQStVUHAyYncwQlpzdUZnK3AxdEc5SHJSa3pLR2ZYK05rWUwwZmxHUjNqcjZWSGpjR1NOdDhLNEZhKzYveUJiVTFlTitVaUk5UTNURkdKKzZ2bmgiLCJtYWMiOiIwMDQzYTNlYzRkN2Y4MTg5OTk1ODY1ZjcxNWI5ODNjOGNlNmFlMTgzNWIzOTlmMjc5ODcwOWYwOTQxZWVmZjAwIiwidGFnIjoiIn0%3D; selected_locale=en; ticketgol_session=eyJpdiI6Im45dld2cExDc3c4d3BYUXhCWldqTUE9PSIsInZhbHVlIjoiMHBqRFZzTjNkKzJhOXRaTXRaMDRCak4xUDZwQVdENFpLWG5NZjBZcEVES3ovQnJGNHdOUGNaaHN4amFXM1d5NlRuVmp0N2xFczBPd0pOdlFvaSs5S2RXOWh4cU11VkVhY1V0d21OTHVlaE10TnY1MUMyR09nVmU3RkJPTTRQYVciLCJtYWMiOiI4M2IxOTc1OTk4ZTRjNjBjMWE0YWM3NWZmMWZhZjMzOTBiOTZlZDRkNWNlMjhiYTU4MWUwOTI3NTA2MWRhNTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">5e003f89-2744-4ae7-a040-887715f2e86c</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.44.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|DA7******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346452405\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1813812352 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5vMzlLcm9rZ0F4dDRSZmpsWVpoanc9PSIsInZhbHVlIjoieVNxelJ4T0dpVzhDdXYvOWYvdXF1N3gwTlFIR2FUUENqZGdPWXlDaVdTbXpTQStVUHAyYncwQlpzdUZnK3AxdEc5SHJSa3pLR2ZYK05rWUwwZmxHUjNqcjZWSGpjR1NOdDhLNEZhKzYveUJiVTFlTitVaUk5UTNURkdKKzZ2bmgiLCJtYWMiOiIwMDQzYTNlYzRkN2Y4MTg5OTk1ODY1ZjcxNWI5ODNjOGNlNmFlMTgzNWIzOTlmMjc5ODcwOWYwOTQxZWVmZjAwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im45dld2cExDc3c4d3BYUXhCWldqTUE9PSIsInZhbHVlIjoiMHBqRFZzTjNkKzJhOXRaTXRaMDRCak4xUDZwQVdENFpLWG5NZjBZcEVES3ovQnJGNHdOUGNaaHN4amFXM1d5NlRuVmp0N2xFczBPd0pOdlFvaSs5S2RXOWh4cU11VkVhY1V0d21OTHVlaE10TnY1MUMyR09nVmU3RkJPTTRQYVciLCJtYWMiOiI4M2IxOTc1OTk4ZTRjNjBjMWE0YWM3NWZmMWZhZjMzOTBiOTZlZDRkNWNlMjhiYTU4MWUwOTI3NTA2MWRhNTIxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813812352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1199603228 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 07:38:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199603228\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-643353058 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643353058\", {\"maxDepth\":0})</script>\n"}}