<?php

namespace App\Repositories;

use App\DTO\ClubFilterDTO;
use App\DTO\SearchFilterDTO;
use App\Models\Club;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Class ClubRepository
 *
 * Repository class for interacting with the `Club` model.
 */
class ClubRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * ClubRepository constructor.
     *
     * @param  Club  $model  The underlying model for the repository.
     */
    public function __construct(Club $model)
    {
        $this->model = $model;
    }

    public function getClubOptionsList(): Collection
    {
        return $this->model->select('id')->with(['translation:club_id,name'])
            ->where('is_active', 1)
            ->get()
            ->pluck('translation.name', 'id');
    }

    /**
     * Get Clubs Listing
     *
     * @return Builder Return Clubs list with Pagination
     */
    public function getClubsList(ClubFilterDTO $filterDTO): Builder
    {
        return $this->model->with(['media'])
            ->select(self::getClubTable().'.*', 'ct.name as club_name')
            ->leftJoin(self::getClubTransTable().' as ct', function ($join) {
                $join->on(self::getClubTable().'.id', '=', 'ct.club_id')
                    ->where('ct.locale', app()->getLocale());
            })
            ->where('is_active', 1)
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('ct.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('ct.title', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->countries), function ($query) use ($filterDTO) {
                $query->whereIn(self::getClubTable().'.country_id', $filterDTO->countries);
            })
            ->when(! empty($filterDTO->stadiums), function ($query) use ($filterDTO) {
                $query->whereIn(self::getClubTable().'.stadium_id', $filterDTO->stadiums);
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy(self::getClubTable().'.id', 'desc');
            });
    }

    /**
     * Search clubs
     *
     * @return Builder Return Clubs list query with search
     */
    public function searchClubs(SearchFilterDTO $searchDTO): Builder
    {
        return $this->model->select(self::getClubTable().'.id', 'ct.name')
            ->addSelect(DB::raw("'club' as type"))
            ->leftJoin(self::getClubTransTable().' as ct', function ($join) {
                $join->on(self::getClubTable().'.id', '=', 'ct.club_id')
                    ->where('ct.locale', app()->getLocale());
            })
            ->where('is_active', 1)
            ->where('ct.name', 'LIKE', "%{$searchDTO->search}%")
            ->orderBy(self::getClubTable().'.id', 'desc');
    }

    /**
     * Get Club Detail
     *
     * @param  string  $slug
     * @return Model Return Club detail object
     */
    public function getClubDetail($slug): ?Model
    {
        $relations = ['translation', 'media', 'country.translation'];

        return $this->model->whereSlug($slug)->where('is_active', 1)
            ->with($relations)
            ->first();
    }
}
