import { Calendar, MapPin, Blocks, Tickets, Info } from "lucide-react";
import { Link } from "@inertiajs/react";
import EventDateTime from "@/components/eventdetails/EventDateTime";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useTranslations from "@/hooks/useTranslations";

export default function TicketDetailSection() {
    const { translate } = useTranslations();
    const { reservation } = useTicketCheckout();

    const badgeColors = {
        [reservation.ticketTypeEnums.E_TICKET]: "badge-error",
        [reservation.ticketTypeEnums.MOBILE_TICKET]: "badge-secondary",
    };
    const badgeClass =
        badgeColors[reservation.ticket.ticket_type.toLowerCase()] ||
        "badge-neutral";

    return (
        <>
            <div className="px-5 mb-5">
                <h2 className="mt-5 text-xl font-bold leading-tight">
                    <Link
                        href={route(
                            "detail.show",
                            reservation.ticket.event.localized_slug.slug,
                        )}
                        className="hover:underline"
                    >
                        {reservation.ticket.event.translation.name}
                    </Link>
                </h2>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Calendar className="w-4 h-4" />
                    <EventDateTime event={reservation.ticket.event} />
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700 hover:underline">
                    <MapPin className="w-4 h-4" />
                    <Link
                        href={route(
                            "detail.show",
                            reservation.ticket.event.stadium.localized_slug
                                .slug,
                        )}
                    >
                        <span>
                            {reservation.ticket.event.stadium.translation.name},{" "}
                            {reservation.ticket.event.stadium.address_line_1},{" "}
                            {reservation.ticket.event.stadium.address_line_2},{" "}
                            {reservation.ticket.event.country.translation.name},{" "}
                            {reservation.ticket.event.stadium.postcode}
                        </span>
                    </Link>
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Blocks className="w-4 h-4" />
                    <p>{reservation.ticket.sector.translation.name}</p>
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Tickets className="w-4 h-4" />
                    <p>
                        {reservation.quantity}{" "}
                        {translate("ticket.tickets_text")}
                    </p>
                </div>
                <div className="mt-3 flex items-center gap-2 text-sm text-gray-700">
                    <p
                        className={`badge badge-outline capitalize ${badgeClass}`}
                    >
                        {reservation.ticket.ticket_type}
                    </p>
                </div>
            </div>
        </>
    );
}
