<?php

namespace App\Http\Controllers;

use App\Models\Slug;
use App\Services\CmsPageService;
use Inertia\Inertia;
use Inertia\Response;

class DetailPageController extends Controller
{
    protected $cmsPageService;

    public function __construct(
        CmsPageService $cmsPageService,
    ) {
        $this->cmsPageService = $cmsPageService;
    }

    /**
     * Display the detail pages of Event, Club, Stadium, League, CmsPage
     */
    public function show($slug): Response
    {
        try {
            $locale = app()->getLocale();

            $slugRecord = Slug::where('slug', $slug)
                ->where('locale', $locale)
                ->first();

            if (! $slugRecord) {
                return Inertia::render('Error', ['status' => 404]);
            }

            $model = $slugRecord->sluggable;
            $modelName = class_basename($model);

            if ($modelName == 'CmsPage') {
                $cmsPage = $this->cmsPageService->getCmsPageDetailBySlug($slug);

                return Inertia::render('CMSPage', [
                    'cmsData' => $cmsPage,
                ]);
            } else {
                $component = "{$modelName}s/Show";

                return Inertia::render($component, [
                    'slug' => $model->localizedSlug->slug,
                ]);
            }
        } catch (\Exception $e) {
            return Inertia::render('Error');
        }
    }
}
