<?php

namespace Database\Factories;

use App\Enums\EventCategoryType;
use App\Models\Club;
use App\Models\Country;
use App\Models\Language;
use App\Models\League;
use App\Models\Restriction;
use App\Models\Stadium;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = EventCategoryType::getValues();
        $country = Country::whereIn('id', Stadium::select('country_id')->distinct())->inRandomOrder()->first();

        $clubs = Club::where('country_id', $country->id)->inRandomOrder()->take(2)->get();

        return [
            'date' => $this->faker->dateTimeBetween('now', '+1 year'),
            'time' => $this->faker->time,
            'timezone' => $this->faker->timezone,
            'is_feature_event' => rand(0, 1),
            'category' => $categories[rand(0, count($categories) - 1)],
            'country_id' => $country->id,
            'stadium_id' => Stadium::where('country_id', $country->id)->inRandomOrder()->first(),
            'home_club_id' => $clubs[0]->id,
            'guest_club_id' => $clubs[1]->id,
            'league_id' => League::where('country_id', $country->id)->inRandomOrder()->first(),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($event) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];
            $slugs = [];

            foreach ($languages as $value) {
                $name = $this->faker->name;
                $translationData[] = [
                    'event_id' => $event->id,
                    'locale' => $value,
                    'name' => $name,
                    'description' => $this->faker->paragraph(),
                    'meta_title' => $this->faker->sentence,
                    'meta_description' => $this->faker->sentence,
                    'meta_keywords' => $this->faker->sentence,
                ];

                $slugs[$value] = Str::slug('event-'.$name.'-'.$event->id);
            }

            $event->translations()->createMany($translationData);

            $event->setSlugs($slugs);

            $sectorIds = $event->stadium->sectors()->inRandomOrder()->limit(3)->pluck('id');

            $event->stadiumSectors()->sync($sectorIds);

            $restrictionIds = Restriction::where('type', 'event')->inRandomOrder()->limit(3)->pluck('id');
            $event->restrictions()->sync($restrictionIds);
        });
    }
}
