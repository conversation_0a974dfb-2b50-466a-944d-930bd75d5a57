<?php

namespace App\Models;

use App\Traits\CascadeSoftDeletes;
use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class League extends Model implements HasMedia
{
    // ** @use HasFactory<\Database\Factories\ClubFactory> */
    use CascadeSoftDeletes, CustomActivityLog, HasFactory, HasSlugs, InteractsWithMedia, SoftDeletes;

    protected $fillable = ['season_id', 'country_id', 'is_published'];

    protected $cascadeDeletes = ['events'];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function season()
    {
        return $this->belongsTo(Season::class, 'season_id', 'id');
    }

    public function events()
    {
        return $this->hasMany(Event::class, 'league_id', 'id');
    }

    public function translations()
    {
        return $this->hasMany(LeagueTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(LeagueTranslation::class, 'league_id', 'id')
            ->where('locale', app()->getLocale());
    }
}
