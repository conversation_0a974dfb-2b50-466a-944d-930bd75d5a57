<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;

class Club extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\ClubFactory> */
    use CustomActivityLog, HasFactory, HasSlugs, InteractsWithMedia, SoftDeletes;

    public function translations()
    {
        return $this->hasMany(ClubTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(ClubTranslation::class, 'club_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function stadium()
    {
        return $this->belongsTo(Stadium::class);
    }
}
