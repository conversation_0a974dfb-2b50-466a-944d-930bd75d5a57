import React from "react";
import { Search, Filter } from "lucide-react";
import useTranslations from "@/hooks/useTranslations";
import useOrderStatuses from "@/hooks/useOrderStatuses";
import SelectInput from "@/components/forms/SelectInput";
import TextInput from "@/components/forms/TextInput";
import DatePicker from "@/components/forms/DatePicker";
import useOrders from "@/hooks/useOrders";

const OrderFilters = ({ filters, isLoading }) => {
    const { translate } = useTranslations();
    const { setFilter, fetchOrders, clearFilters } = useOrders();
    const { statuses: statusOptions, isLoading: statusesLoading } =
        useOrderStatuses();

    const handleSearch = (e) => {
        e.preventDefault();
        fetchOrders();
    };

    return (
        <form onSubmit={handleSearch}>
            <div className="p-4 mb-6 bg-white rounded-lg shadow">
                <div className="flex flex-col gap-4 mb-4 md:flex-row">
                    {/* Search Bar */}
                    <div className="flex-1">
                        <div className="relative">
                            <TextInput
                                type="text"
                                value={filters.search}
                                onChange={(e) =>
                                    setFilter("search", e.target.value)
                                }
                                placeholder={translate(
                                    "orders.search",
                                    "Search orders...",
                                )}
                                className="pr-10"
                            />
                            <button
                                type="submit"
                                className="absolute right-2 top-1/2 text-gray-500 transform -translate-y-1/2 hover:text-primary"
                            >
                                <Search size={18} className="mr-3" />
                            </button>
                        </div>
                    </div>

                    {/* Status Filter */}
                    <div className="w-full md:w-1/3">
                        <SelectInput
                            options={statusOptions}
                            value={statusOptions.find(
                                (opt) => opt.value === filters.status,
                            )}
                            onChange={(option) =>
                                setFilter("status", option?.value || "")
                            }
                            placeholder={translate(
                                "order.filter_by_status",
                                "Filter by Status",
                            )}
                            isLoading={statusesLoading}
                            menuPortalTarget={document.body}
                        />
                    </div>
                </div>

                {/* Date Filter */}
                <div className="flex flex-col gap-4 mb-4 md:flex-row">
                    <div className="flex-1">
                        <label className="block mb-1 text-sm font-medium text-gray-700">
                            {translate("order.date_from", "Date From")}
                        </label>
                        <DatePicker
                            value={filters.date_from}
                            onChange={(value) => setFilter("date_from", value)}
                            placeholder={translate(
                                "order.select_date_from",
                                "Select start date",
                            )}
                            maxDate={filters.date_to || undefined}
                        />
                    </div>
                    <div className="flex-1">
                        <label className="block mb-1 text-sm font-medium text-gray-700">
                            {translate("order.date_to", "Date To")}
                        </label>
                        <DatePicker
                            value={filters.date_to}
                            onChange={(value) => setFilter("date_to", value)}
                            placeholder={translate(
                                "order.select_date_to",
                                "Select end date",
                            )}
                            minDate={filters.date_from || undefined}
                        />
                    </div>
                </div>

                {/* Filter Actions */}
                <div className="flex gap-6 justify-end">
                    <button
                        onClick={clearFilters}
                        className="btn btn-outline"
                        disabled={isLoading}
                    >
                        {translate("common.clear", "Clear")}
                    </button>
                    <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={isLoading}
                    >
                        <Filter size={16} className="mr-1" />
                        {translate("common.apply_filters", "Apply Filters")}
                    </button>
                </div>
            </div>
        </form>
    );
};

export default OrderFilters;
