{"__meta": {"id": "X3fbc7e45869813607b59fb7826bcd3af", "datetime": "2025-06-11 04:00:40", "utime": **********.496547, "method": "POST", "uri": "/api/v1/events", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.408148, "end": **********.496572, "duration": 0.0884239673614502, "duration_str": "88.42ms", "measures": [{"label": "Booting", "start": **********.408148, "relative_start": 0, "end": **********.46804, "relative_end": **********.46804, "duration": 0.05989193916320801, "duration_str": "59.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.46806, "relative_start": 0.05991196632385254, "end": **********.496575, "relative_end": 3.0994415283203125e-06, "duration": 0.028515100479125977, "duration_str": "28.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5503048, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/events", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@index", "namespace": null, "where": [], "as": "api.events.index", "prefix": "api/v1/events", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=46\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:46-60</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0054800000000000005, "accumulated_duration_str": "5.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy' limit 1", "type": "query", "params": [], "bindings": ["JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.473598, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 17.153}, {"sql": "select count(*) as aggregate from (select `events`.*, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-06-11' and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-06-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 44}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.4827192, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "EventService.php:44", "source": {"index": 16, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=44", "ajax": false, "filename": "EventService.php", "line": "44"}, "connection": "ticketgol", "explain": null, "start_percent": 17.153, "width_percent": 56.204}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQkJGWGJkNEFqVDdjZXBTbDBaRTlTZGxmUUhCTG53dFZISU5nTUhjTiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiQkJGWGJkNEFqVDdjZXBTbDBaRTlTZGxmUUhCTG53dFZISU5nTUhjTiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.492554, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 73.358, "width_percent": 26.642}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BBFXbd4AjT7cepSl0ZE9SdlfQHBLnwtVHINgMHcN", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2062b8-d424-4af7-a183-8744f0dcdf63\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events", "status_code": "<pre class=sf-dump id=sf-dump-698720448 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-698720448\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-58975506 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-58975506\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-447180823 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>categories</span>\" => []\n  \"<span class=sf-dump-key>stadiums</span>\" => []\n  \"<span class=sf-dump-key>clubs</span>\" => []\n  \"<span class=sf-dump-key>countries</span>\" => []\n  \"<span class=sf-dump-key>leagues</span>\" => []\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447180823\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1290719043 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IjRlQUxhK0tXbHFvWUJCOVFFM0lRbVE9PSIsInZhbHVlIjoiVk9yYzZFcTJwaFpUY0xnbFJNODNBa3ZPSHZOTUZqa21zbkIyWWMxUDB4Wlg0M1lqQUZyQUJjK3RDQ21SNmUzazI4RTFqZEpzbnU3TC9FQUxENnZXOXlnMng1R28rS1RsS0Z0eTFMMm5EQm0wZWMrd2RvNVNLRFNMdVZ5NnFlS1oiLCJtYWMiOiJlZmVlMjkwOWY4NTcxNGZiZmY3OGE1MWNlN2M0YTA4MTkzMDIwN2UwMTM3YTUwOGNjNDc3MTliNDY3ODhjNTg2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkdiUlZzd0RkdEhxK3FMdXRwWnFSZGc9PSIsInZhbHVlIjoiZmtYRkxaUWx3SlJFK2xXTWM3QjRjV0xZTTA3MUlWd1FKUmNxLzVhcFliaWw3L20wMTZHUEZ0QTdqQzVralZySXFwUThnc0w2QnB6UUR5cDNHNnJJdFZ6aDl4NVdHNUVOc2JCQkdRTkdkdnh2VnFUWFd1UnM0RzZGcVdxeDRGKzkiLCJtYWMiOiIyZDE0OWM3N2M4ZDczNTE1NjVjZDg1MTFmYjc5Y2Y0YjlhZDM2NjhiMTljN2VjODJjZDJkYTUyOGNjNTVmMmU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRlQUxhK0tXbHFvWUJCOVFFM0lRbVE9PSIsInZhbHVlIjoiVk9yYzZFcTJwaFpUY0xnbFJNODNBa3ZPSHZOTUZqa21zbkIyWWMxUDB4Wlg0M1lqQUZyQUJjK3RDQ21SNmUzazI4RTFqZEpzbnU3TC9FQUxENnZXOXlnMng1R28rS1RsS0Z0eTFMMm5EQm0wZWMrd2RvNVNLRFNMdVZ5NnFlS1oiLCJtYWMiOiJlZmVlMjkwOWY4NTcxNGZiZmY3OGE1MWNlN2M0YTA4MTkzMDIwN2UwMTM3YTUwOGNjNDc3MTliNDY3ODhjNTg2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290719043\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-273571294 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BBFXbd4AjT7cepSl0ZE9SdlfQHBLnwtVHINgMHcN</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273571294\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-748803145 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 04:00:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InQxaE1BRHFHNXNyVWRhZ1dLZ241bXc9PSIsInZhbHVlIjoiUHU0UkZFK1A2QTlUV05aV2xYMUp1aUp1ZVIvaURUUmxQUTJISklkYmlJK3B3Q0NFY3A0Qk40blJTS2swU09HLzZ2RXU0RS9pRGZmNVliMWtCWFNWVXZQS21XQlU1MXBPNkx5TytLZDFSSUxObEpMNVlLSjdRZUNPS0wwdDdnOGkiLCJtYWMiOiI0YTViNGQzOWMyYjRkMmJlZmY0MzExMWZiYzU2YWZmMDI2NDYwMDZkYjlkZWY5OGM1ODZhM2Y0OWZmNzgwMzE0IiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 06:00:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImFoRmVFZkdEVTViVGp4bHhzamNRTVE9PSIsInZhbHVlIjoiUTVEMDBiWGFWV3FIK1Z5Nm8xdkVYSFdpc0k3SEppVWFUbVYvYW5WUXBkK0FQVXcvb1JUWGZSK0hZbG5KNkNpQzhnS1NZWGV1LzRRR3dydU8wWFRVZitJZ21DRTQ4UGlObjZ6c2xXV25tQlFuck03cTRiS3FYY21rMGo1U1NBS0UiLCJtYWMiOiJlNWZhYTNhYjdjZmE5MjVjNjA1MzNjNjBhNTExYzcyNjFlNzdjYzdkNDU2MjU5MTMwZjk3MGUyNTk1OWMyMzhmIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 06:00:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InQxaE1BRHFHNXNyVWRhZ1dLZ241bXc9PSIsInZhbHVlIjoiUHU0UkZFK1A2QTlUV05aV2xYMUp1aUp1ZVIvaURUUmxQUTJISklkYmlJK3B3Q0NFY3A0Qk40blJTS2swU09HLzZ2RXU0RS9pRGZmNVliMWtCWFNWVXZQS21XQlU1MXBPNkx5TytLZDFSSUxObEpMNVlLSjdRZUNPS0wwdDdnOGkiLCJtYWMiOiI0YTViNGQzOWMyYjRkMmJlZmY0MzExMWZiYzU2YWZmMDI2NDYwMDZkYjlkZWY5OGM1ODZhM2Y0OWZmNzgwMzE0IiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 06:00:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImFoRmVFZkdEVTViVGp4bHhzamNRTVE9PSIsInZhbHVlIjoiUTVEMDBiWGFWV3FIK1Z5Nm8xdkVYSFdpc0k3SEppVWFUbVYvYW5WUXBkK0FQVXcvb1JUWGZSK0hZbG5KNkNpQzhnS1NZWGV1LzRRR3dydU8wWFRVZitJZ21DRTQ4UGlObjZ6c2xXV25tQlFuck03cTRiS3FYY21rMGo1U1NBS0UiLCJtYWMiOiJlNWZhYTNhYjdjZmE5MjVjNjA1MzNjNjBhNTExYzcyNjFlNzdjYzdkNDU2MjU5MTMwZjk3MGUyNTk1OWMyMzhmIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 06:00:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748803145\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1395708193 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BBFXbd4AjT7cepSl0ZE9SdlfQHBLnwtVHINgMHcN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395708193\", {\"maxDepth\":0})</script>\n"}}