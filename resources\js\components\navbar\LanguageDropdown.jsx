import { useState } from "react";
import useLanguage from "@/hooks/useLanguage";

function LanguageDropdown({ translate }) {
    const { locale, handleLanguageChange } = useLanguage();
    const [isOpen, setIsOpen] = useState(false);
    const languages = Object.entries(translate("enums.languages"));

    const selectedLabel =
        languages.find(([key]) => key === locale)?.[1] || "Select";

    return (
        <div className="dropdown dropdown-end">
            <div
                tabIndex={0}
                role="button"
                className="btn btn-sm btn-outline btn-primary w-full max-w-xs"
                onClick={() => setIsOpen(!isOpen)}
            >
                {selectedLabel}
            </div>
            {isOpen && (
                <ul
                    tabIndex={0}
                    className="dropdown-content menu p-1 shadow bg-base-100 rounded-box w-40 z-20"
                >
                    {languages.map(([key, value]) => (
                        <li key={key}>
                            <a
                                onClick={() => {
                                    handleLanguageChange(key);
                                    setIsOpen(false);
                                }}
                            >
                                {value}
                            </a>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}

export default LanguageDropdown;
