<?php

namespace App\Services;

use App\DTO\OrderFilterDTO;
use App\Enums\OrderStatus;
use App\Http\Resources\OrderCollection;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;

class OrderService
{
    private $orderRepository;

    private $orderTransactionRepository;

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
    }

    // Current implementation
    public function getPaginatedOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = $this->orderRepository->getOrdersForUser($userId, $filtersDTO);
        $orders = $query->paginate(config('services.ticketgol.items_per_page'));

        return new OrderCollection($orders);
    }

    public function getStatusOptions()
    {
        $options = collect(OrderStatus::cases())->map(function ($status) {
            return [
                'label' => $status->getLabel(),
                'value' => $status->value,
            ];
        });

        $options->unshift([
            'label' => __('common.all') ?? 'All',
            'value' => '',
        ]);

        return $options;
    }
}
