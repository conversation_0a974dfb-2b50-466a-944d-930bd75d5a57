<?php

namespace App\Repositories;

use App\DTO\EventFilterDTO;
use App\DTO\SearchFilterDTO;
use App\Models\Event;
use App\Models\Ticket;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * Class EventRepository
 *
 * Repository class for interacting with the `Event` model.
 */
class EventRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * EventRepository constructor.
     *
     * @param  Event  $model  The underlying model for the repository.
     */
    public function __construct(Event $model)
    {
        $this->model = $model;
    }

    protected function getEventListCommonQuery()
    {
        return $this->model->with(['media', 'stadium:id', 'league:id'])
            ->select(self::getEventTable().'.*', 'et.name as event_name', 'st.name as stadium_name', 'lt.name as league_name')
            ->leftJoin(self::getEventTransTable().' as et', function ($join) {
                $join->on(self::getEventTable().'.id', '=', 'et.event_id')
                    ->where('et.locale', app()->getLocale());
            })
            ->leftJoin(self::getStadiumTransTable().' as st', function ($join) {
                $join->on(self::getEventTable().'.stadium_id', '=', 'st.stadium_id')
                    ->where('st.locale', app()->getLocale());
            })
            ->leftJoin(self::getLeagueTransTable().' as lt', function ($join) {
                $join->on(self::getEventTable().'.league_id', '=', 'lt.league_id')
                    ->where('lt.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as hc', function ($join) {
                $join->on(self::getEventTable().'.home_club_id', '=', 'hc.club_id')
                    ->where('hc.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as gc', function ($join) {
                $join->on(self::getEventTable().'.guest_club_id', '=', 'gc.club_id')
                    ->where('gc.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->where('date', '>=', date('Y-m-d'))
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id'),
            ])
            ->havingNotNull('min_price');
    }

    /**
     * Get Events Listing
     *
     * @return Builder Return events list query
     */
    public function getEventsList(EventFilterDTO $filterDTO): Builder
    {
        return $this->getEventListCommonQuery()
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('et.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('st.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('lt.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('hc.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('gc.name', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->categories), function ($query) use ($filterDTO) {
                $query->whereIn('category', $filterDTO->categories);
            })
            ->when(! empty($filterDTO->stadiums), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.stadium_id', $filterDTO->stadiums);
            })
            ->when(! empty($filterDTO->leagues), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.league_id', $filterDTO->leagues);
            })
            ->when(! empty($filterDTO->countries), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.country_id', $filterDTO->countries);
            })
            ->when(! empty($filterDTO->clubs), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->whereIn('home_club_id', $filterDTO->clubs)
                        ->orWhereIn('guest_club_id', $filterDTO->clubs);
                });
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('date', 'asc');
            });
    }

    /**
     * Get Home page events Listing
     *
     * @return Builder Return events list query
     */
    public function getHomePageEvents($filters = []): Builder
    {
        return $this->getEventListCommonQuery()
            ->when(! empty($filters['upcoming']), function ($query) {
                $query->where('is_feature_event', 0)
                    ->orderBy('date', 'asc');
            })
            ->when(! empty($filters['featured']), function ($query) {
                $query->where('is_feature_event', 1);
            });
    }

    /**
     * Search Events
     *
     * @return Builder Return events list query with search filter
     */
    public function searchEvents(SearchFilterDTO $searchDTO): Builder
    {
        return $this->model->select(self::getEventTable().'.id', 'date', 'et.name')
            ->leftJoin(self::getEventTransTable().' as et', function ($join) {
                $join->on(self::getEventTable().'.id', '=', 'et.event_id')
                    ->where('et.locale', app()->getLocale());
            })
            ->where('et.name', 'LIKE', "%{$searchDTO->search}%")
            ->where('date', '>=', date('Y-m-d'))
            ->where('is_published', 1)
            ->addSelect(DB::raw("'event' as type"))
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id'),
            ])
            ->orderBy('events.id', 'desc')
            ->havingNotNull('min_price');
    }

    /**
     * Get Events Detail
     *
     * @param  string  $slug
     * @return Model Return event detail object
     */
    public function getEventDetail($slug): ?Model
    {
        $relations = ['translation', 'media', 'stadium.translation', 'stadium.country.translation', 'league.translation', 'homeClub.translation', 'guestClub.translation', 'stadiumSectors.translation', 'restrictions.translation'];

        return $this->model->whereSlug($slug)
            ->with($relations)
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id'),
            ])
            ->addSelect([
                'max_price' => Ticket::select(DB::raw('MAX(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id'),
            ])
            ->where('is_published', 1)
            ->first();
    }

    public function getEventWithImage(int $eventId): ?Event
    {
        $model = $this->model->with([
            'media' => function ($query) {
                $query->limit(Event::FIRST_IMAGE);
            },
            'translation',
            'homeClub.translation',
            'guestClub.translation',
            'stadium.translation',
            'country.translation',
        ])
            ->select([
                'id',
                'date',
                'time',
                'home_club_id',
                'guest_club_id',
                'stadium_id',
                'country_id',
            ])
            ->findOrFail($eventId);

        $model->imageUrl = $model->media->first()?->getUrl();

        return $model;
    }
}
