import { useEffect } from "react";
import { Head, usePage, Link } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";

import AppLayout from "@/layouts/AppLayout";
import LeagueCard from "@/components/leagues/LeagueCard";
import LeagueSidebar from "@/components/leagues/LeagueSidebar";
import SelectInput from "@/components/forms/SelectInput";
import useLeagues from "@/hooks/useLeagues";
import useTranslations from "@/hooks/useTranslations";

function Leagues() {
    const { translate } = useTranslations();
    const {
        leagues,
        filters,
        updateFilter,
        fetchOptions,
        loading,
        loadMoreLeagues,
        clearNextPageUrl,
        nextPageUrl,
        fetchLeaguesInitially,
    } = useLeagues();

    useEffect(() => {
        clearNextPageUrl();
        fetchOptions();

        const timer = setTimeout(() => {
            fetchLeaguesInitially();
        }, 10);

        return () => clearTimeout(timer);
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: loading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreLeagues,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("leagues.head_title", "Leagues")} />
            <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8">
                <LeagueSidebar />
                <main className="md:w-3/4 w-full">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl md:text-4xl font-bold">
                            {translate("leagues.page_title", "All Leagues")}
                        </h1>
                        <SelectInput
                            wrapperClass="w-1/2 md:w-1/3"
                            options={translate("leagues.sort_options")}
                            value={
                                filters.sort
                                    ? translate("leagues.sort_options").find(
                                          (option) =>
                                              option.value === filters.sort,
                                      )
                                    : null
                            }
                            onChange={(selected) =>
                                updateFilter("sort", selected.value)
                            }
                            placeholder={translate(
                                "leagues.sort_by_placeholder",
                            )}
                        />
                    </div>

                    <div className="h-[800px] overflow-y-auto my-5">
                        {!loading && leagues.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate(
                                        "leagues.no_leagues",
                                        "Sorry, no leagues match your filters.",
                                    )}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    {translate("leagues.no_leagues_details")}
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {leagues.map((league, index) => (
                                        <Link
                                            key={`${league.id}-${index}`}
                                            href={route(
                                                "detail.show",
                                                league.localized_slug.slug,
                                            )}
                                            className="hover:scale-[1.02] transition-transform duration-150"
                                        >
                                            <LeagueCard league={league} />
                                        </Link>
                                    ))}
                                </div>

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}

                                {loading && (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                </main>
            </div>
        </>
    );
}

Leagues.layout = (page) => <AppLayout>{page}</AppLayout>;

export default Leagues;
