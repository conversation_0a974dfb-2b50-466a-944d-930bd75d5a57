import { useEffect, useState, useCallback } from "react";
import { Link } from "@inertiajs/react";
import { Search } from "lucide-react";
import SearchListResult from "@/components/search/SearchListResult";
import { searchResultTypeIcon, highlight } from "@/helpers/searchResult";
import axios from "axios";
import debounce from "lodash/debounce";
import useTranslations from "@/hooks/useTranslations";

export default function SearchBox() {
    const { translate } = useTranslations();

    const [query, setQuery] = useState("");
    const [results, setResults] = useState([]);
    const [total, setTotal] = useState(0);
    const [showDropdown, setShowDropdown] = useState(false);

    const fetchSuggestions = async (search) => {
        if (!search) {
            setResults([]);
            setShowDropdown(false);
            setTotal(0);
            return;
        }

        try {
            const response = await axios.get(route("api.search.suggestions"), {
                params: { q: search },
            });
            setResults(response.data.suggestions.data);
            setTotal(response.data.suggestions.total);
            setShowDropdown(true);
        } catch (err) {
            console.error(err);
        }
    };

    const debouncedFetch = useCallback(debounce(fetchSuggestions, 300), []);

    const handleChange = (e) => {
        const val = e.target.value;
        setQuery(val);
        debouncedFetch(val);
    };

    return (
        <div className="relative w-full max-w-2xl mx-auto">
            <div className="join w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                    value={query}
                    onChange={handleChange}
                    className="input input-bordered pl-10 w-full text-gray-600"
                    placeholder={translate("common.search_placeholder")}
                />
            </div>

            {showDropdown && (
                <div className="absolute mt-1 w-full bg-white border shadow rounded text-left max-h-[350px] overflow-y-auto">
                    <ul className="divide-y">
                        {results.length > 0 ? (
                            <>
                                {results.map((suggestion) => (
                                    <SearchListResult
                                        key={`${suggestion.type}-${suggestion.id}`}
                                        result={suggestion}
                                        query={query}
                                    />
                                ))}
                                {query.trim() && total > 10 && (
                                    <li className="p-3 text-blue-600 hover:bg-gray-100 cursor-pointer text-center font-medium">
                                        <Link
                                            href={route("search", {
                                                q: encodeURIComponent(query),
                                            })}
                                        >
                                            {translate(
                                                "common.view_all_results",
                                            )}
                                        </Link>
                                    </li>
                                )}
                            </>
                        ) : (
                            <li className="p-3">
                                {translate("common.no_search_results_found")}
                            </li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
}
