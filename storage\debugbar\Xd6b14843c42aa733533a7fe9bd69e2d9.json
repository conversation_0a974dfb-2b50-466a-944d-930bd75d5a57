{"__meta": {"id": "Xd6b14843c42aa733533a7fe9bd69e2d9", "datetime": "2025-06-11 03:57:45", "utime": 1749614265.24475, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749614265.088615, "end": 1749614265.24477, "duration": 0.15615510940551758, "duration_str": "156ms", "measures": [{"label": "Booting", "start": 1749614265.088615, "relative_start": 0, "end": 1749614265.155631, "relative_end": 1749614265.155631, "duration": 0.0670161247253418, "duration_str": "67.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749614265.155648, "relative_start": 0.06703305244445801, "end": 1749614265.244774, "relative_end": 4.0531158447265625e-06, "duration": 0.0891261100769043, "duration_str": "89.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5499240, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "Auth/Login", "param_count": null, "params": [], "start": 1749614265.172223, "type": "react", "hash": "reactE:\\Ticketgol\\Code\\Ticketgol\\resources\\js/Pages/Auth/Login.jsxAuth/Login", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fresources%2Fjs%2FPages%2FAuth%2FLogin.jsx&line=1", "ajax": false, "filename": "Login.jsx", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, App\\Http\\Middleware\\UnderConstructionMiddleware, guest", "domain": "ticketgol.test", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=19\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:19-25</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019569999999999997, "accumulated_duration_str": "19.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy' limit 1", "type": "query", "params": [], "bindings": ["JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": 1749614265.161252, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 5.365}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiYURIMGJjUnl1WldBdWhUZW4xQk9EaWpaWnA0RzBHdWswRDVzdURpUCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = 1749614265, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiYURIMGJjUnl1WldBdWhUZW4xQk9EaWpaWnA0RzBHdWswRDVzdURpUCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", 1749614265, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1749614265.2245288, "duration": 0.01852, "duration_str": "18.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 5.365, "width_percent": 94.635}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2061ad-6a3d-4529-8712-4992754e88da\" target=\"_blank\">View in Telescope</a>", "path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1321762757 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1321762757\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1922814397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922814397\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2105567899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2105567899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1088821105 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6InA5NHgyYzZaSzNQYWJLWDlmVS80Q0E9PSIsInZhbHVlIjoibTVIWkc2TzRFaXNnN0kxWmtZOExleVlBdEozWlpwbGlVa3RPYzRVOU4xZFp4ZlMyMUxJd0l4RmczaDdtWWJlbW55dnUrb3lON1lhUzQ0QWlsTjJKbjFhMW1sQm14Y3JKQllEekFIUW1rcWpGNUlVZEdiRVhJR2Nac1ErR2w3SVIiLCJtYWMiOiJiNTZjMmE2MzUwM2Q2YTA3YTcwNTJjM2FhMzQwODg4YmU3ZTFjNWE3MzMzZWUwMjZiZGUyNGJjMDBkOTQwZWIyIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Ims5SXE5cWkxcVlnazd6NG1WUXZnL0E9PSIsInZhbHVlIjoienZmRzRuK1lrd3A5cWc0ZE5MQ1ZJRlArRzY3V2hPLzVtMHJpeXpKWlBYQWg2MHVEbnQ3bmUrbmdUeDE0QVBDbGhUL0lQYW1OZVhIZW5zclEycEJnNHlKMi9ZZ1c1VXZVaDhCYkt2MDdVTU5jYk96T2JtQm5US0xlRkxNcW5MdXIiLCJtYWMiOiIzZmEzYjczYWU2YmViODlkOTQ2OTc5MGEyMzQ5NDU3Njc3MzI1ZDg5YmZhYmI2Y2E3ZTM5YmMyYTNmMGE0MTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088821105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2131585425 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131585425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 03:57:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"97 characters\">selected_locale=en; expires=Thu, 11 Jun 2026 03:57:45 GMT; Max-Age=31536000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImR0SVpBT1JYajcvTEJEeXk0eDVpeFE9PSIsInZhbHVlIjoiWXhzbVZxUnR0U3RiSjFQemdzNWNtUDg3M3ZWZ1hhT1lyVElIV2JUUWVOek9VdSsraUJERE1vdzdtcnpiRS9OZ3UraGxUV2ZraGQ0OGFtRXNtU05VcGd4aFpkMmxoc0JqNHFSZVc2Z04waTQ0SmRYanZFSXZSRkdTTithV2h0ZVkiLCJtYWMiOiIwMmJjYmI0ODlmNzAxMjU1OTQxNGIzOThlMjZjZmQ2NzhjNDJlYWU3YWNkODZlOTc4MjQ1YWFiYTQwMTgyMTYyIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 05:57:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkJGYTFHSURHWVN6TE1nc3FhaUlkV0E9PSIsInZhbHVlIjoiMkZ6VHdRQXh6bWt2am8xVzFzWS9ta2ozZnBPT3dJVUlacWxRQk1vUmorSW10bTdkL1VacnBBY2ZHdXV0c2NWVm1FS0lBaTFGMjFOM1VlbTBEeEx3ZlJkRmVPZGJubC9SclRNdVc5cmJPVkJ6VkRDaXdQamRSYnZvZHozQmJlTVUiLCJtYWMiOiJkZmFjODJhMWU1M2MwODdmYzFjNjkxY2M1NWZiYTY3YjgyY2NiNGE0YWE5M2IxODRmNWZjYjc5YmY1MjVhYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 05:57:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">selected_locale=en; expires=Thu, 11-Jun-2026 03:57:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImR0SVpBT1JYajcvTEJEeXk0eDVpeFE9PSIsInZhbHVlIjoiWXhzbVZxUnR0U3RiSjFQemdzNWNtUDg3M3ZWZ1hhT1lyVElIV2JUUWVOek9VdSsraUJERE1vdzdtcnpiRS9OZ3UraGxUV2ZraGQ0OGFtRXNtU05VcGd4aFpkMmxoc0JqNHFSZVc2Z04waTQ0SmRYanZFSXZSRkdTTithV2h0ZVkiLCJtYWMiOiIwMmJjYmI0ODlmNzAxMjU1OTQxNGIzOThlMjZjZmQ2NzhjNDJlYWU3YWNkODZlOTc4MjQ1YWFiYTQwMTgyMTYyIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 05:57:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkJGYTFHSURHWVN6TE1nc3FhaUlkV0E9PSIsInZhbHVlIjoiMkZ6VHdRQXh6bWt2am8xVzFzWS9ta2ozZnBPT3dJVUlacWxRQk1vUmorSW10bTdkL1VacnBBY2ZHdXV0c2NWVm1FS0lBaTFGMjFOM1VlbTBEeEx3ZlJkRmVPZGJubC9SclRNdVc5cmJPVkJ6VkRDaXdQamRSYnZvZHozQmJlTVUiLCJtYWMiOiJkZmFjODJhMWU1M2MwODdmYzFjNjkxY2M1NWZiYTY3YjgyY2NiNGE0YWE5M2IxODRmNWZjYjc5YmY1MjVhYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 05:57:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}