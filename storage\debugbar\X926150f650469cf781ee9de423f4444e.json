{"__meta": {"id": "X926150f650469cf781ee9de423f4444e", "datetime": "2025-06-11 05:37:31", "utime": **********.167215, "method": "GET", "uri": "/api/v1/clubs/filters", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.044376, "end": **********.167229, "duration": 0.12285304069519043, "duration_str": "123ms", "measures": [{"label": "Booting", "start": **********.044376, "relative_start": 0, "end": **********.110403, "relative_end": **********.110403, "duration": 0.06602716445922852, "duration_str": "66.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.110415, "relative_start": 0.0660390853881836, "end": **********.167231, "relative_end": 2.1457672119140625e-06, "duration": 0.05681610107421875, "duration_str": "56.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5907696, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/clubs/filters", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\ClubController@getFilters", "namespace": null, "where": [], "as": "api.clubs.filters", "prefix": "api/v1/clubs", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FClubController.php&line=55\" onclick=\"\">app/Http/Controllers/Api/V1/ClubController.php:55-70</a>"}, "queries": {"nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0174, "accumulated_duration_str": "17.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u' limit 1", "type": "query", "params": [], "bindings": ["0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.1137679, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.506}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.116965, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 3.506, "width_percent": 3.908}, {"sql": "select `id` from `countries` where `is_published` = 1 and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/ClubController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\ClubController.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.120471, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 7.414, "width_percent": 7.701}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/ClubController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\ClubController.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.12986, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 15.115, "width_percent": 12.529}, {"sql": "select `id` from `stadiums` where `is_published` = 1 and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/ClubController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\ClubController.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1456978, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 27.644, "width_percent": 7.356}, {"sql": "select `stadium_id`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (2, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/ClubController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\ClubController.php", "line": 59}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.149436, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 35, "width_percent": 24.655}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (2, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/ClubController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\ClubController.php", "line": 59}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.156136, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 59.655, "width_percent": 5}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiblZ3dWRYYnljYzhYV1I2UGRzdWx6M21OZXVHd2hVVEZrWktFeE1DTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkT1hoMTBrSUVReGpaUDIzNVFRVUZpZXJxVi91UGQvTjZ0RE81YkFobDlXUHM2VFZ5anNqdVMiO30=', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = '0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiblZ3dWRYYnljYzhYV1I2UGRzdWx6M21OZXVHd2hVVEZrWktFeE1DTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkT1hoMTBrSUVReGpaUDIzNVFRVUZpZXJxVi91UGQvTjZ0RE81YkFobDlXUHM2VFZ5anNqdVMiO30=", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.160219, "duration": 0.00615, "duration_str": "6.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 64.655, "width_percent": 35.345}]}, "models": {"data": {"App\\Models\\Country": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 517, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/events\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$OXh10kIEQxjZP235QQUFierqV/uPd/N6tDO5bAhl9WPs6TVyjsjuS"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f20855b-3507-4bd9-964f-256b40712524\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/clubs/filters", "status_code": "<pre class=sf-dump id=sf-dump-321611259 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-321611259\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-51813947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-51813947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-608857331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608857331\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1246167556 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6Im1oTEZNeWpLN00wY2NzRlFsMWg3VkE9PSIsInZhbHVlIjoiYU5zT1k3c29wT1VkdWJRczQ0eXA1NXRoelNueTFMdkpKSm51eWlTbkJzTHFTd2RnT2N0QU54THorNUtNbThVUVNoaERIWHNtSlhPV0R5aVliUXlINTY1SWpzVGtxQ09TRUx0dERDV2ZNZ0hlYXBWZEp0aXNNUnVMZ3dqWHo4TUciLCJtYWMiOiI5NGM1ZDgxZGZhYTlkN2ZkYmZiYTA0ZDQwNmEzNmNlNzA4Mzk5ZDg2ODgyMGY4MGE1N2U4NmRlYzI0NTgyNWU2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InEzcWtIVlpoRkNyNC9rYm12ZXV2Y1E9PSIsInZhbHVlIjoiUFA5UldkSEZjT1hDSHBRWmFmWkRPRjF2d2UwN2xvL2NCdWs3bTk1Zk1reTRvSnVwdzdoQk5NSjZDQlZDNHVmcVRUMmpKd2VnWlNZMkhFNW5hamZKYzlNMGVTb3ZpTHZvNzdmbGs5Qkg5ckpmdU8rcldsb2hmVlkrd1dkTnAweTEiLCJtYWMiOiJmMWNmMGRkZjM0YTJiZTE5MzBlYmQ0MTgzMjQ5Mjk2NTMwMWIyMGMzY2NlYTE5ZjZiNGVmYjU2MjhkMjYyMmI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/clubs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1oTEZNeWpLN00wY2NzRlFsMWg3VkE9PSIsInZhbHVlIjoiYU5zT1k3c29wT1VkdWJRczQ0eXA1NXRoelNueTFMdkpKSm51eWlTbkJzTHFTd2RnT2N0QU54THorNUtNbThVUVNoaERIWHNtSlhPV0R5aVliUXlINTY1SWpzVGtxQ09TRUx0dERDV2ZNZ0hlYXBWZEp0aXNNUnVMZ3dqWHo4TUciLCJtYWMiOiI5NGM1ZDgxZGZhYTlkN2ZkYmZiYTA0ZDQwNmEzNmNlNzA4Mzk5ZDg2ODgyMGY4MGE1N2U4NmRlYzI0NTgyNWU2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246167556\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1825518832 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825518832\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 05:37:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxIRGhhWExOTzhtTFNyVm8yeXhoNUE9PSIsInZhbHVlIjoiRG9IM1NuL1NqRUVFb3FiQW5lWkhmRkZubUFLemxhQUVZSXV1RlhnTktqanRha1NPRHgzS0k1SGJiWWV5YW53Vm1lU1dtdk9aUVFqOHI0SUVKdURiWTJUZ09iMjZwVE1jcnhVa1pESDI5TmluamRCTzI1RzRqTFNadlFUaXd6SVkiLCJtYWMiOiI1YjM1MTljZmYwMTM3YzVjNjkzMjI2MGRlMjMwZjQ4ZTNjN2VmZjYwMTY4NzZhODMwMzQxMDY4NTI3NWQ5NjIyIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 07:37:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IjZwTUJudjNjWWVRVkZaSjVmdERpSGc9PSIsInZhbHVlIjoiSjVPTy9XVXVuek9qcm8ycFRFY2pyUTBKczh1ZlloVk4zNldHdldTMytWUHhSekRUQm14cjYxbHZzN2hYcmtzSFJ2T01PQXB2bTUxT1dFSFdEQ0lWU3hHaGkyNm0zallFc0pyS1dzSXB2MzJicytEOUdzeDVzcXhCTGpLUVE3bi8iLCJtYWMiOiJkZDNkOGQwODkyZDhiNmI0NjgwMmY0ZWM0MmY2YTBkNTQ5MTVlYmY0MWExYTRiM2Y2MDc1YTI2NDNkNTNiOGNjIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 07:37:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxIRGhhWExOTzhtTFNyVm8yeXhoNUE9PSIsInZhbHVlIjoiRG9IM1NuL1NqRUVFb3FiQW5lWkhmRkZubUFLemxhQUVZSXV1RlhnTktqanRha1NPRHgzS0k1SGJiWWV5YW53Vm1lU1dtdk9aUVFqOHI0SUVKdURiWTJUZ09iMjZwVE1jcnhVa1pESDI5TmluamRCTzI1RzRqTFNadlFUaXd6SVkiLCJtYWMiOiI1YjM1MTljZmYwMTM3YzVjNjkzMjI2MGRlMjMwZjQ4ZTNjN2VmZjYwMTY4NzZhODMwMzQxMDY4NTI3NWQ5NjIyIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 07:37:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IjZwTUJudjNjWWVRVkZaSjVmdERpSGc9PSIsInZhbHVlIjoiSjVPTy9XVXVuek9qcm8ycFRFY2pyUTBKczh1ZlloVk4zNldHdldTMytWUHhSekRUQm14cjYxbHZzN2hYcmtzSFJ2T01PQXB2bTUxT1dFSFdEQ0lWU3hHaGkyNm0zallFc0pyS1dzSXB2MzJicytEOUdzeDVzcXhCTGpLUVE3bi8iLCJtYWMiOiJkZDNkOGQwODkyZDhiNmI0NjgwMmY0ZWM0MmY2YTBkNTQ5MTVlYmY0MWExYTRiM2Y2MDc1YTI2NDNkNTNiOGNjIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 07:37:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$OXh10kIEQxjZP235QQUFierqV/uPd/N6tDO5bAhl9WPs6TVyjsjuS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}