import AppLayout from "@/layouts/AppLayout";
import {
    Calendar,
    MapPin,
    Image,
    ShieldCheck,
    House,
    HousePlus,
} from "lucide-react";
import { Head, usePage, Link } from "@inertiajs/react";
import { useEffect } from "react";
import EventDateTime from "@/components/eventdetails/EventDateTime";
import TicketFilters from "@/components/eventdetails/TicketFilters";
import TicketListingSidebar from "@/components/eventdetails/TicketListingSidebar";
import useTranslations from "@/hooks/useTranslations";
import useEventDetail from "@/hooks/useEventDetail";

function Show() {
    const { translate } = useTranslations();

    const { slug } = usePage().props;
    const { event, getEventDetail, eventLoading } = useEventDetail();

    useEffect(() => {
        getEventDetail(slug);
    }, []);

    if (eventLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head>
                <title>{event.translation.name}</title>
                <meta name="title" content={event.translation.meta_title} />
                <meta
                    name="keywords"
                    content={event.translation.meta_keywords}
                />
                <meta
                    name="description"
                    content={event.translation.meta_description}
                />
            </Head>
            <div className="container mx-auto px-6 py-5">
                {/* Desktop Version */}
                <div className="hidden lg:block sticky top-0 z-10 mb-5">
                    <TicketFilters event={event} />
                </div>

                <div className="flex flex-col lg:flex-row gap-8 items-start">
                    {/* Event Details */}
                    <div className="lg:w-3/5 w-full bg-base-100  rounded-xl overflow-hidden shadow">
                        <div className="px-5 mb-5">
                            <h2 className="mt-5 text-2xl font-bold leading-tight">
                                {event.translation.name}
                                <span className="ml-4 badge badge-dash badge-neutral badge-outline badge-lg">
                                    {event.category_label}
                                </span>
                            </h2>

                            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700">
                                <Calendar className="w-4 h-4" />
                                <EventDateTime event={event} />
                            </div>
                            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                                <MapPin className="w-4 h-4" />
                                <Link
                                    href={route(
                                        "detail.show",
                                        event.stadium.localized_slug.slug,
                                    )}
                                >
                                    <span>
                                        <b>{event.stadium.translation.name},</b>{" "}
                                        {event.stadium.address_line_1},{" "}
                                        {event.stadium.address_line_2},{" "}
                                        {event.stadium.country.translation.name}
                                        , {event.stadium.postcode}
                                    </span>
                                </Link>
                            </div>
                            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                                <ShieldCheck className="w-4 h-4" />
                                <Link
                                    href={route(
                                        "detail.show",
                                        event.league.localized_slug.slug,
                                    )}
                                >
                                    <span className="font-bold">
                                        {event.league.translation.name}
                                    </span>
                                </Link>
                            </div>
                            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                                <House className="w-4 h-4" />
                                <Link
                                    href={route(
                                        "detail.show",
                                        event.home_club.localized_slug.slug,
                                    )}
                                >
                                    <span className="font-bold">
                                        {event.home_club.translation.name}
                                    </span>
                                </Link>
                            </div>
                            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                                <HousePlus className="w-4 h-4" />
                                <Link
                                    href={route(
                                        "detail.show",
                                        event.guest_club.localized_slug.slug,
                                    )}
                                >
                                    <span className="font-bold">
                                        {event.guest_club.translation.name}
                                    </span>
                                </Link>
                            </div>
                        </div>
                        {event.media.length > 0 ? (
                            <img
                                src={event.media[0].original_url}
                                alt={
                                    event.media[0].custom_properties?.alt ||
                                    event.translation.name
                                }
                                className="w-full h-auto"
                            />
                        ) : (
                            <div className="bg-base-200 w-full h-64 flex items-center justify-center rounded-xl text-gray-500">
                                <Image className="w-20 h-20" />
                            </div>
                        )}

                        <div className="px-5 mb-5">
                            {/* About the Event */}

                            <div className="mt-10">
                                <h3 className="text-xl font-bold mb-2">
                                    {translate("events.about_the_event")}
                                </h3>
                                <p className="text-gray-700 leading-relaxed">
                                    {event.translation.description}
                                </p>
                            </div>
                            <div className="divider"></div>
                            <div className="mt-4">
                                <h3 className="text-xl font-bold mb-2">
                                    {translate("events.restriction_text")}
                                </h3>
                                <ul className="list-disc leading-relaxed ml-5">
                                    {event.restrictions.map((restriction) => (
                                        <li key={restriction.id}>
                                            {restriction.translation.name}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Tablet Version */}
                    <div className="block lg:hidden w-full">
                        <TicketFilters event={event} />
                    </div>

                    {/* Tickets listing with filters */}
                    <TicketListingSidebar event={event} />
                </div>
            </div>
        </>
    );
}

Show.layout = (page) => <AppLayout children={page} />;

export default Show;
