{"__meta": {"id": "X80dab8881c7432ee734dad65e051f787", "datetime": "2025-06-11 03:57:45", "utime": **********.283772, "method": "POST", "uri": "/api/v1/stadiums", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[03:57:45] LOG.error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ticketgol.slugs' doesn't exist (Connection: mysql, SQL: select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = en and `slugs`.`sluggable_id` in (3, 4, 7, 9, 10) and `slugs`.`sluggable_type` = App\\Models\\Stadium) {\n    \"exception\": {\n        \"errorInfo\": [\n            \"42S02\",\n            1146,\n            \"Table 'ticketgol.slugs' doesn't exist\"\n        ],\n        \"connectionName\": \"mysql\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.271963, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.170407, "end": **********.283796, "duration": 0.1133890151977539, "duration_str": "113ms", "measures": [{"label": "Booting", "start": **********.170407, "relative_start": 0, "end": **********.229233, "relative_end": **********.229233, "duration": 0.05882596969604492, "duration_str": "58.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.229253, "relative_start": 0.05884599685668945, "end": **********.283799, "relative_end": 2.86102294921875e-06, "duration": 0.05454587936401367, "duration_str": "54.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5628432, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/stadiums", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\StadiumController@index", "namespace": null, "where": [], "as": "api.stadiums.index", "prefix": "api/v1/stadiums", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStadiumController.php&line=28\" onclick=\"\">app/Http/Controllers/Api/V1/StadiumController.php:28-42</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01165, "accumulated_duration_str": "11.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy' limit 1", "type": "query", "params": [], "bindings": ["JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.234496, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 11.416}, {"sql": "select count(*) as aggregate from `stadiums` left join `stadium_translations` as `st` on `stadiums`.`id` = `st`.`stadium_id` and `st`.`locale` = 'en' where `is_published` = 1 and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/StadiumController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StadiumController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.252121, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "StadiumService.php:52", "source": {"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStadiumService.php&line=52", "ajax": false, "filename": "StadiumService.php", "line": "52"}, "connection": "ticketgol", "explain": null, "start_percent": 11.416, "width_percent": 9.099}, {"sql": "select `stadiums`.*, `st`.`name` as `stadium_name` from `stadiums` left join `stadium_translations` as `st` on `stadiums`.`id` = `st`.`stadium_id` and `st`.`locale` = 'en' where `is_published` = 1 and `stadiums`.`deleted_at` is null order by `stadiums`.`id` desc limit 9 offset 0", "type": "query", "params": [], "bindings": ["en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/StadiumController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StadiumController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.256631, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "StadiumService.php:52", "source": {"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStadiumService.php&line=52", "ajax": false, "filename": "StadiumService.php", "line": "52"}, "connection": "ticketgol", "explain": null, "start_percent": 20.515, "width_percent": 7.639}, {"sql": "select * from `media` where `media`.`model_id` in (3, 4, 7, 9, 10) and `media`.`model_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StadiumController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StadiumController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.260669, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "StadiumService.php:52", "source": {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStadiumService.php&line=52", "ajax": false, "filename": "StadiumService.php", "line": "52"}, "connection": "ticketgol", "explain": null, "start_percent": 28.155, "width_percent": 11.76}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiYURIMGJjUnl1WldBdWhUZW4xQk9EaWpaWnA0RzBHdWswRDVzdURpUCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiYURIMGJjUnl1WldBdWhUZW4xQk9EaWpaWnA0RzBHdWswRDVzdURpUCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.275085, "duration": 0.007, "duration_str": "7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 39.914, "width_percent": 60.086}]}, "models": {"data": {"App\\Models\\Stadium": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2061ad-7978-4eff-9a44-7bc5423fb0cb\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/stadiums", "status_code": "<pre class=sf-dump id=sf-dump-1986478755 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-1986478755\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1062250349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1062250349\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1301986120 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>countries</span>\" => []\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301986120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1439809297 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IlhiMFdaaXo0dHlrRUZmZDRIUjFUbFE9PSIsInZhbHVlIjoiY0pQNnJWSjkzcEpiU3hVaWRlc1R6bTl4aWhNa3dldSsvTDF4TnVZZVkzaG0ybEtFaHg4S0plZmRsellrRXVqVjhNRDFON1RRRWhpMHJEbHVKOHhYTTVMKzdVOWZkd0ZkUXd1cFdQVzFKUGVCdzhxcjczUUdJS0NhZ2V3Qk9RYS8iLCJtYWMiOiI4YzczMWRlNzIzODc2YzJkMmNhMzY5YzZkOWQ2NDYyMjI3M2IxOGM5NDQwYjY5MmVhMzE5MGY2YmE3YjAyY2Y4IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Ik9scVpyNkJVY0xPdlFqekxVSGhhL1E9PSIsInZhbHVlIjoidWVMZkN2WVJFL05FU2pIa3R5N29jMGJlRjVCVGZQa0ZWUXd6d3BQSElicGZQKzBWOUtpREZKNEhXdDVlRS9SdmxCd1VBUlhmUkM5Z3Rka1B0QmNTM0tCZGtRT2xCR01Rci9iNXFCSUZLdjNwRk02RUd5aGliY0lwMGVHRFpqNGgiLCJtYWMiOiI5ZDEzZmRkMzNhZDI0NGI3MzY0OWRjZmUwZTNmN2NiZjYxNTYxMWQzNmM2MGQ4ZjMxMmUwMWY5N2NjMDhhYjQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://ticketgol.test/stadiums</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlhiMFdaaXo0dHlrRUZmZDRIUjFUbFE9PSIsInZhbHVlIjoiY0pQNnJWSjkzcEpiU3hVaWRlc1R6bTl4aWhNa3dldSsvTDF4TnVZZVkzaG0ybEtFaHg4S0plZmRsellrRXVqVjhNRDFON1RRRWhpMHJEbHVKOHhYTTVMKzdVOWZkd0ZkUXd1cFdQVzFKUGVCdzhxcjczUUdJS0NhZ2V3Qk9RYS8iLCJtYWMiOiI4YzczMWRlNzIzODc2YzJkMmNhMzY5YzZkOWQ2NDYyMjI3M2IxOGM5NDQwYjY5MmVhMzE5MGY2YmE3YjAyY2Y4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">38</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439809297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-361934865 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JNJyxFAIezbjyvqtgflZZpTulei5bsZFLgpfSpAy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361934865\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1345263574 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 03:57:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFwdkQzZ0lPOVdGUFU2TUVray9LblE9PSIsInZhbHVlIjoiNWljL0tDbmJTb2taQWpOUmtCd2ljQWNrT1JQRzJDakw0cm1oUk42SmJoK1JIWnBKL05YTnV0eTRMMGdCS0JUdWlTZG5MeGZKdEJZVGgxMHZlL1lFU1hUK3BuZWhpeXhPY0padUpsdjdPK0IrdTdtZ01POVd5Q1RRd0ZWR2Q0SUgiLCJtYWMiOiI4ZGQ3NWRiMDY3YTkxOTUxZTczMDRkYWQ4MTVkMzVkOTAzM2MwNDJhNmM4OTNkNjRlMzQ1YWRmZjc0ODc2NTY3IiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 05:57:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImplQSt3dXgwQTZWMWFXNnQySGZUVFE9PSIsInZhbHVlIjoia0MvOWt5SkFlQ0x2TlhMdkEzMTJFVDNQRzlGY2NBQk5rTDdCL2g5MjFHY3p1cHJWNk5pdWoxMGhlRjU5QmJkMTNlbFdFanZjdHZ5Z2I2RUwvcjQ4SVIvTzE1cGlaU1grckRwK3VlNHBZclVsVlJoSUdLSkhOenlHQWk2cURXYzgiLCJtYWMiOiJkNzVjNjM2ZTQyYmRmN2E2YTFjNTUzNjQwZWU5NDllMDQzMzg5OTNhOGUzNzA5Yzc5Yjg3NTU2N2IyNzc5Mjk5IiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 05:57:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFwdkQzZ0lPOVdGUFU2TUVray9LblE9PSIsInZhbHVlIjoiNWljL0tDbmJTb2taQWpOUmtCd2ljQWNrT1JQRzJDakw0cm1oUk42SmJoK1JIWnBKL05YTnV0eTRMMGdCS0JUdWlTZG5MeGZKdEJZVGgxMHZlL1lFU1hUK3BuZWhpeXhPY0padUpsdjdPK0IrdTdtZ01POVd5Q1RRd0ZWR2Q0SUgiLCJtYWMiOiI4ZGQ3NWRiMDY3YTkxOTUxZTczMDRkYWQ4MTVkMzVkOTAzM2MwNDJhNmM4OTNkNjRlMzQ1YWRmZjc0ODc2NTY3IiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 05:57:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImplQSt3dXgwQTZWMWFXNnQySGZUVFE9PSIsInZhbHVlIjoia0MvOWt5SkFlQ0x2TlhMdkEzMTJFVDNQRzlGY2NBQk5rTDdCL2g5MjFHY3p1cHJWNk5pdWoxMGhlRjU5QmJkMTNlbFdFanZjdHZ5Z2I2RUwvcjQ4SVIvTzE1cGlaU1grckRwK3VlNHBZclVsVlJoSUdLSkhOenlHQWk2cURXYzgiLCJtYWMiOiJkNzVjNjM2ZTQyYmRmN2E2YTFjNTUzNjQwZWU5NDllMDQzMzg5OTNhOGUzNzA5Yzc5Yjg3NTU2N2IyNzc5Mjk5IiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 05:57:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345263574\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2059293651 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aDH0bcRyuZWAuhTen1BODijZZp4G0Guk0D5suDiP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059293651\", {\"maxDepth\":0})</script>\n"}}