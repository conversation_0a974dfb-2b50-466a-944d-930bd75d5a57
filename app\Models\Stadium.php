<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spa<PERSON>\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;

class Stadium extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\StadiumFactory> */
    use CustomActivityLog, HasFactory, HasSlugs, InteractsWithMedia, SoftDeletes;

    protected $table = 'stadiums';

    public function translations()
    {
        return $this->hasMany(StadiumTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(StadiumTranslation::class, 'stadium_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function sectors()
    {
        return $this->hasMany(StadiumSector::class, 'stadium_id', 'id');
    }

    public function clubs()
    {
        return $this->hasMany(Club::class, 'stadium_id', 'id');
    }
}
