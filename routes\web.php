<?php

use App\Http\Controllers\ClubController;
use App\Http\Controllers\DetailPageController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LeagueController;
use App\Http\Controllers\MyAccountController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\StadiumController;
use App\Http\Controllers\TicketController;
use App\Http\Middleware\UnderConstructionMiddleware;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::domain(config('services.app.domain'))->group(function () {
    Route::get('/under-construction', function () {
        return Inertia::render('UnderConstruction');
    })->name('frontend.maintenance');

    Route::middleware([UnderConstructionMiddleware::class])->group(function () {

        Route::get('/', [HomeController::class, 'index'])->name('home');
        Route::get('/search', [SearchController::class, 'index'])->name('search');

        Route::get('/events', [EventController::class, 'index'])->name('events');

        Route::get('/clubs', [ClubController::class, 'index'])->name('clubs');

        Route::get('/leagues', [LeagueController::class, 'index'])->name('leagues');

        Route::get('/stadiums', [StadiumController::class, 'index'])->name('stadiums');

        Route::middleware(['auth'])->group(function () {
            Route::group(['prefix' => 'my-account'], function () {
                Route::get('/', [MyAccountController::class, 'index'])->name('my-account.index');

                Route::group(['prefix' => 'profile'], function () {
                    Route::get('/', [ProfileController::class, 'edit'])->name('profile.edit');
                    Route::patch('/', [ProfileController::class, 'update'])->name('profile.update');
                    Route::delete('/', [ProfileController::class, 'destroy'])->name('profile.destroy');
                });

                Route::get('/orders', [MyAccountController::class, 'orders'])->name('my-account.orders');
                Route::get('/tickets', [MyAccountController::class, 'tickets'])->name('my-account.tickets');
                Route::get('/support', [MyAccountController::class, 'support'])->name('my-account.support');
            });
        });

        Route::middleware(['auth', 'verified'])->group(function () {
            Route::get('/dashboard', function () {
                return Inertia::render('Dashboard');
            })->name('dashboard');

            Route::get('/ticket/checkout/{reservationId}', [TicketController::class, 'checkout'])->name('ticket.checkout');
            Route::get('/checkout/success', [PaymentController::class, 'success'])->name('checkout.success');
            Route::get('/checkout/cancel', [PaymentController::class, 'cancel'])->name('checkout.cancel');
        });

        require __DIR__.'/auth.php';

        Route::get('/{slug}', [DetailPageController::class, 'show'])
            ->where('slug', '.*')
            ->name('detail.show');
    });
});
