import { Calendar, MapPin, Image, ShieldCheck } from "lucide-react";
import { Link, router } from "@inertiajs/react";
import dayjs from "dayjs";

export default function EventCard({
    event,
    translationFrom,
    translationBuyTickets,
}) {
    const goToEvent = () => {
        router.visit(route("detail.show", event.localized_slug.slug));
    };

    const goToStadium = (e) => {
        e.stopPropagation();
        router.visit(route("detail.show", event.stadium.localized_slug.slug));
    };

    const goToLeague = (e) => {
        e.stopPropagation();
        router.visit(route("detail.show", event.league.localized_slug.slug));
    };
    return (
        <div
            className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow rounded-md"
            onClick={goToEvent}
        >
            <figure className="relative h-48 bg-blue-200">
                {event.media.length > 0 && event.media[0].original_url ? (
                    <img
                        src={event.media[0].original_url}
                        alt={
                            event.media[0].custom_properties?.alt ||
                            event.event_name
                        }
                        className="w-full h-full object-cover"
                    />
                ) : (
                    <img
                        className="w-[90%] mx-auto bg-blue-200"
                        src="/img/ticketgol-logo.png"
                        alt={
                            event.media[0]?.custom_properties?.alt ||
                            event.event_name
                        }
                    />
                )}
                <div className="absolute top-4 right-4 badge badge-warning">
                    {translationFrom} €{event.min_price}
                </div>
            </figure>
            <div className="card-body p-5">
                <div
                    className="tooltip tooltip-neutral w-fit text-left"
                    data-tip={event.event_name}
                    tabIndex={0}
                >
                    <h2 className="card-title line-clamp-1 max-w-xs">
                        {event.event_name}
                    </h2>
                </div>
                <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span className="text-sm">
                        {dayjs(event.date).format("DD/MM/YYYY")}
                    </span>
                </div>
                <div className="flex items-center text-gray-600 hover:underline">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm truncate" onClick={goToStadium}>
                        {event.stadium_name}
                    </span>
                </div>
                <div className="flex items-center text-gray-600 hover:underline">
                    <ShieldCheck className="w-4 h-4 mr-2" />
                    <span className="text-sm truncate" onClick={goToLeague}>
                        {event.league_name}
                    </span>
                </div>
                <div className="card-actions justify-end mt-2">
                    <button className="btn btn-primary btn-sm">
                        {translationBuyTickets}
                    </button>
                </div>
            </div>
        </div>
    );
}
