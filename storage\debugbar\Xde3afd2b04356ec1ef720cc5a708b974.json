{"__meta": {"id": "Xde3afd2b04356ec1ef720cc5a708b974", "datetime": "2025-06-11 04:15:16", "utime": **********.179333, "method": "POST", "uri": "/api/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.791804, "end": **********.179349, "duration": 0.3875448703765869, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.791804, "relative_start": 0, "end": **********.8969, "relative_end": **********.8969, "duration": 0.10509586334228516, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.896914, "relative_start": 0.10510993003845215, "end": **********.179351, "relative_end": 2.1457672119140625e-06, "duration": 0.2824370861053467, "duration_str": "282ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5427736, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/auth/login", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\AuthController@login", "namespace": null, "where": [], "as": "api.login", "prefix": "api", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FAuthController.php&line=12\" onclick=\"\">app/Http/Controllers/Api/V1/AuthController.php:12-24</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013499999999999998, "accumulated_duration_str": "13.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 394}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Http/Requests/Api/V1/LoginRequest.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Requests\\Api\\V1\\LoginRequest.php", "line": 34}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V1/AuthController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\AuthController.php", "line": 15}], "start": **********.916904, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 11.778}, {"sql": "delete from `sessions` where `id` = 'bhMTRTsU0IHUKB7u5ijtBhdEubOApNTENyfj2qDH'", "type": "query", "params": [], "bindings": ["bhMTRTsU0IHUKB7u5ijtBhdEubOApNTENyfj2qDH"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 269}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 555}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 526}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 402}], "start": **********.121765, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:269", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 269}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=269", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "269"}, "connection": "ticketgol", "explain": null, "start_percent": 11.778, "width_percent": 5.333}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/CauserResolver.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\CauserResolver.php", "line": 99}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/CauserResolver.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\CauserResolver.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/CauserResolver.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\CauserResolver.php", "line": 44}], "start": **********.1262012, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 17.111, "width_percent": 4.667}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `batch_uuid`, `event`, `description`, `updated_at`, `created_at`) values ('Access', '{\\\"ip\\\":\\\"127.0.0.1\\\",\\\"user_agent\\\":\\\"PostmanRuntime\\\\/7.44.0\\\"}', 7, 'App\\\\Models\\\\User', null, 'Login', '<PERSON><PERSON> logged in', '2025-06-11 04:15:16', '2025-06-11 04:15:16')", "type": "query", "params": [], "bindings": ["Access", "{\"ip\":\"127.0.0.1\",\"user_agent\":\"PostmanRuntime\\/7.44.0\"}", 7, "App\\Models\\User", null, "<PERSON><PERSON>", "<PERSON><PERSON> logged in", "2025-06-11 04:15:16", "2025-06-11 04:15:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AccessLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AccessLogger.php", "line": 27}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 772}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 540}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 402}], "start": **********.129293, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 21.778, "width_percent": 36.222}, {"sql": "select * from `users` where `users`.`id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Requests/Api/V1/LoginRequest.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Requests\\Api\\V1\\LoginRequest.php", "line": 42}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/AuthController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\AuthController.php", "line": 15}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.136172, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "LoginRequest.php:42", "source": {"index": 21, "namespace": null, "name": "app/Http/Requests/Api/V1/LoginRequest.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Requests\\Api\\V1\\LoginRequest.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FRequests%2FApi%2FV1%2FLoginRequest.php&line=42", "ajax": false, "filename": "LoginRequest.php", "line": "42"}, "connection": "ticketgol", "explain": null, "start_percent": 58, "width_percent": 4.074}, {"sql": "delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\\\Models\\\\User' and `personal_access_tokens`.`tokenable_id` = 7 and `personal_access_tokens`.`tokenable_id` is not null and `name` = 'asus'", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7, "asus"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/AuthController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\AuthController.php", "line": 17}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.140083, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:17", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/AuthController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\AuthController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FAuthController.php&line=17", "ajax": false, "filename": "AuthController.php", "line": "17"}, "connection": "ticketgol", "explain": null, "start_percent": 62.074, "width_percent": 4.889}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('asus', '6f2c9102c4e8b0842c57f43d9d2b888495da55a57f8b47f6923c7efbc49807d5', '[\\\"*\\\"]', null, 7, 'App\\\\Models\\\\User', '2025-06-11 04:15:16', '2025-06-11 04:15:16')", "type": "query", "params": [], "bindings": ["asus", "6f2c9102c4e8b0842c57f43d9d2b888495da55a57f8b47f6923c7efbc49807d5", "[\"*\"]", null, 7, "App\\Models\\User", "2025-06-11 04:15:16", "2025-06-11 04:15:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/AuthController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\AuthController.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.144243, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:64", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=64", "ajax": false, "filename": "HasApiTokens.php", "line": "64"}, "connection": "ticketgol", "explain": null, "start_percent": 66.963, "width_percent": 33.037}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2067f1-032d-4d68-b6a0-9be3976a324f\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-1969851307 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1969851307\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1321982140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321982140\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1588473869 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>device_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asus</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588473869\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-18882044 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">407</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">selected_locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">409cb830-9522-4a3f-bb1e-f53473cec30d</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.44.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">multipart/form-data; boundary=--------------------------369186521790120824038181</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18882044\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-407376373 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407376373\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1841222658 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 04:15:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841222658\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-277175793 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277175793\", {\"maxDepth\":0})</script>\n"}}