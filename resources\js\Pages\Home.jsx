import {
    Search,
    ArrowRight,
    Globe2,
    <PERSON>,
    <PERSON>,
    Ticket,
    CreditCard,
} from "lucide-react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import { useEffect, useState } from "react";
import axios from "axios";

import AppLayout from "@/layouts/AppLayout";
import EventCarousel from "@/components/events/EventCarousel";
import SearchBox from "@/components/search/SearchBox";
import { homeTestimonials } from "@/constants/testimonials";
import useTranslations from "@/hooks/useTranslations";

function Home() {
    const { translate } = useTranslations();

    const [upcomingEvents, setUpcomingEvents] = useState([]);
    const [featuredEvents, setFeaturedEvents] = useState([]);

    const [query, setQuery] = useState("");
    const [showDropdown, setShowDropdown] = useState(true);

    useEffect(() => {
        const getEvents = async () => {
            try {
                const response = await axios.get(route("api.home.index"));
                if (response.data.success) {
                    setUpcomingEvents(response.data.upcoming);
                    setFeaturedEvents(response.data.featured);
                }
            } catch (error) {}
        };

        getEvents();
    }, []);

    return (
        <>
            <Head title={translate("common.welcome")} />
            <div className="flex-grow">
                <div className="min-h-screen">
                    <div
                        className="hero min-h-[600px] relative z-10"
                        style={{
                            backgroundImage: "url('/img/banner.png')",
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                        }}
                    >
                        <div className="hero-overlay bg-opacity-80"></div>
                        <div className="hero-content text-center text-neutral-content">
                            <div className="max-w-3xl">
                                <h1 className="mb-5 text-5xl font-bold text-white">
                                    {translate("common.banner_title")}
                                </h1>
                                <p className="mb-8 text-lg">
                                    {translate("common.banner_description")}
                                </p>

                                {/* Search Bar */}
                                <SearchBox />
                            </div>
                        </div>
                    </div>
                    {upcomingEvents.length > 0 && (
                        <div className="container mx-auto px-5 py-12">
                            <div className="flex justify-between items-center">
                                <h2 className="text-3xl font-bold pl-3">
                                    {translate("common.upcoming_events")}
                                </h2>
                                <Link
                                    href={route("events")}
                                    className="btn btn-ghost gap-2"
                                >
                                    {translate("common.view_all_text")}{" "}
                                    <ArrowRight className="w-4 h-4" />
                                </Link>
                            </div>
                            <EventCarousel key={0} events={upcomingEvents} />
                        </div>
                    )}
                    {featuredEvents.length > 0 && (
                        <div className="container max-auto px-5 py-12">
                            <div className="flex justify-between items-center">
                                <h2 className="text-3xl font-bold pl-3">
                                    {translate("common.featured_events")}
                                </h2>
                                <Link
                                    href={route("events")}
                                    className="btn btn-ghost gap-2"
                                >
                                    {translate("common.view_all_text")}{" "}
                                    <ArrowRight className="w-4 h-4" />
                                </Link>
                            </div>
                            <EventCarousel key={1} events={featuredEvents} />
                        </div>
                    )}
                </div>
            </div>

            <div>
                {/* Customer Testimonials */}
                <div className="bg-gray-200 py-16">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12">
                            {translate("common.customer_say_title")}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {homeTestimonials.map((testimonial, index) => (
                                <div
                                    key={index}
                                    className="card bg-base-100 shadow-xl"
                                >
                                    <div className="card-body">
                                        <div className="flex items-center mb-4">
                                            {[...Array(testimonial.rating)].map(
                                                (_, i) => (
                                                    <Star
                                                        key={i}
                                                        className="w-5 h-5 text-yellow-400 fill-current"
                                                    />
                                                ),
                                            )}
                                        </div>
                                        <p className="mb-4">
                                            "{testimonial.comment}"
                                        </p>
                                        <div className="text-sm text-gray-600">
                                            <p className="font-semibold">
                                                {testimonial.name}
                                            </p>
                                            <p>{testimonial.location}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Core Features Section */}
                <div className="py-16">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12">
                            {translate("common.features_title")}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            <div className="card bg-base-100 shadow-xl">
                                <div className="card-body items-center text-center">
                                    <Search className="w-12 h-12 text-primary mb-4" />
                                    <h3 className="card-title">
                                        {translate("common.feature_1_title")}
                                    </h3>
                                    <p>
                                        {translate(
                                            "common.feature_1_description",
                                        )}
                                    </p>
                                </div>
                            </div>
                            <div className="card bg-base-100 shadow-xl">
                                <div className="card-body items-center text-center">
                                    <Globe2 className="w-12 h-12 text-primary mb-4" />
                                    <h3 className="card-title">
                                        {translate("common.feature_2_title")}
                                    </h3>
                                    <p>
                                        {translate(
                                            "common.feature_2_description",
                                        )}
                                    </p>
                                </div>
                            </div>
                            <div className="card bg-base-100 shadow-xl">
                                <div className="card-body items-center text-center">
                                    <CreditCard className="w-12 h-12 text-primary mb-4" />
                                    <h3 className="card-title">
                                        {translate("common.feature_3_title")}
                                    </h3>
                                    <p>
                                        {translate(
                                            "common.feature_3_description",
                                        )}
                                    </p>
                                </div>
                            </div>
                            <div className="card bg-base-100 shadow-xl">
                                <div className="card-body items-center text-center">
                                    <Ticket className="w-12 h-12 text-primary mb-4" />
                                    <h3 className="card-title">
                                        {translate("common.feature_4_title")}
                                    </h3>
                                    <p>
                                        {translate(
                                            "common.feature_4_description",
                                        )}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

Home.layout = (page) => <AppLayout children={page} />;

export default Home;
