{"__meta": {"id": "Xd70bdfe6afabcc94c8933b318923aa3e", "datetime": "2025-06-10 07:39:14", "utime": **********.997956, "method": "POST", "uri": "/api/v1/orders", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.914035, "end": **********.997969, "duration": 0.08393383026123047, "duration_str": "83.93ms", "measures": [{"label": "Booting", "start": **********.914035, "relative_start": 0, "end": **********.952405, "relative_end": **********.952405, "duration": 0.03836989402770996, "duration_str": "38.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.952414, "relative_start": 0.03837895393371582, "end": **********.997972, "relative_end": 3.0994415283203125e-06, "duration": 0.04555797576904297, "duration_str": "45.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5985080, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "where": [], "as": "api.orders.index", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=21\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:21-27</a>"}, "queries": {"nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00788, "accumulated_duration_str": "7.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'jEocaL9RADlq4cjTF3PRQ2sLLcnRgHER0pBirFP6' limit 1", "type": "query", "params": [], "bindings": ["jEocaL9RADlq4cjTF3PRQ2sLLcnRgHER0pBirFP6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.9588299, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 6.98}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.962409, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 6.98, "width_percent": 8.503}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.967545, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 15.482, "width_percent": 6.345}, {"sql": "select `id`, `order_no`, `buyer_id`, `ticket_id`, `quantity`, `total_price`, `status`, `purchase_date`, `created_at` from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.969323, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 21.827, "width_percent": 5.457}, {"sql": "select `id`, `ticket_no`, `event_id`, `sector_id` from `tickets` where `tickets`.`id` in (82, 83, 84, 85, 86, 87, 88, 89, 90) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.97157, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 27.284, "width_percent": 7.234}, {"sql": "select `id`, `slug` from `events` where `events`.`id` in (2, 5, 8, 10) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.974394, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 34.518, "width_percent": 6.218}, {"sql": "select `id`, `event_id`, `name` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (2, 5, 8, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.976839, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 40.736, "width_percent": 6.599}, {"sql": "select * from `media` where `media`.`model_id` in (2, 5, 8, 10) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.97907, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 47.335, "width_percent": 7.614}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (19, 23, 50, 78, 144) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.981723, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 54.949, "width_percent": 7.107}, {"sql": "select `id`, `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (19, 23, 50, 78, 144)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.98426, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 62.056, "width_percent": 13.071}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (69, 70, 71, 72, 73, 74, 75, 76, 77)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.98742, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 75.127, "width_percent": 10.152}, {"sql": "select `id`, `order_id`, `status`, `currency_code`, `total_amount`, `paid_at`, `created_at` from `order_transactions` where `order_transactions`.`order_id` in (69, 70, 71, 72, 73, 74, 75, 76, 77)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.991688, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 85.279, "width_percent": 7.487}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXhYUzkxZFFBZGw5S3dsS1R2T0RnQmRmZDNsTm9oVXFqbXZHakVqNSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'jEocaL9RADlq4cjTF3PRQ2sLLcnRgHER0pBirFP6'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXhYUzkxZFFBZGw5S3dsS1R2T0RnQmRmZDNsTm9oVXFqbXZHakVqNSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "jEocaL9RADlq4cjTF3PRQ2sLLcnRgHER0pBirFP6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.996508, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 92.766, "width_percent": 7.234}]}, "models": {"data": {"App\\Models\\Attendee": {"value": 51, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\Order": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Event": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 84, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ExXS91dQAdl9KwlKTvODgBdfd3lNohUqjmvGjEj5", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f1eade8-07d9-427f-8fe0-f0aafaec2e9f\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2008605928 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IkZydmEvdmQ2ZU1mU0NOYzdteFg1aVE9PSIsInZhbHVlIjoieVFpbWdGWmYwNi9lcXdJQmFUSEVYU1lUZlpuWk90Qk1FbFJLR3IrVmVCcVFrcU9ML01idHg2dXFaMmJQQlVxU3RlSEZZejlhN01IM2Y5QlZkUlp3Nzl4cVJ3TFI3WDBvKzZJMkdxNmJBcDEyRDNFSC8vZTArNTlTRlFMUURNbEEiLCJtYWMiOiIwM2M1ZWUwYTJhNjg0NDVhYjYyMWNmMzk1ZjYwODIyMTYxOGI1YjNjYzA3YmJhYWRhNTUxNjI1MDI4ZjkzNzRmIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Ikx3bGduM011VFdzK1VnQlluTHo1SlE9PSIsInZhbHVlIjoiRWt1c2NPRmxjYmx4MjdZUzhPaFcrY2l2d3ZVS253SEo3bGIyOTliV2ZnVTVrNlJ6OVRsRjA5anBHVmJ0YkxlTjlkcVEwanZEWU1sRktMYlRRZTRhb0JaT0oranM0TFFQdnJDVFhiV2EvU1BHbVFhZVpXaVY4Sm1BaC9jalVjK2wiLCJtYWMiOiJmODkwMzUwYThmYjY1YjBkMDVhMjYwNDJjNDYzM2EzN2RmZjk3YjJhNWE5ZWIzNTA4YWQ1ZmVhNmY1YjM4YzQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZydmEvdmQ2ZU1mU0NOYzdteFg1aVE9PSIsInZhbHVlIjoieVFpbWdGWmYwNi9lcXdJQmFUSEVYU1lUZlpuWk90Qk1FbFJLR3IrVmVCcVFrcU9ML01idHg2dXFaMmJQQlVxU3RlSEZZejlhN01IM2Y5QlZkUlp3Nzl4cVJ3TFI3WDBvKzZJMkdxNmJBcDEyRDNFSC8vZTArNTlTRlFMUURNbEEiLCJtYWMiOiIwM2M1ZWUwYTJhNjg0NDVhYjYyMWNmMzk1ZjYwODIyMTYxOGI1YjNjYzA3YmJhYWRhNTUxNjI1MDI4ZjkzNzRmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008605928\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-328216258 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ExXS91dQAdl9KwlKTvODgBdfd3lNohUqjmvGjEj5</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEocaL9RADlq4cjTF3PRQ2sLLcnRgHER0pBirFP6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328216258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1335972128 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 07:39:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImdlbmdyQXdqTlVVY1FzejNJUnJ6SWc9PSIsInZhbHVlIjoiMStnVFBTbWtJaWdxTUFqOTkxMUZXaVBxZmM3L0dHSnlhVnlWaWFIRkhpSElUZkl4UGF1bk50MWlJdXpqMVdZQnJRSjZiMW9GUitDYytCZUdHNEVENUtGMTZTY2pBTEYveG9oelJvTVdOaWJZUklaT1Jsa1p3b2RkajVwS1hFVFMiLCJtYWMiOiJmNWUwZWQ1Zjk3YjIyYzc0Njk5ZGQwMTY4Zjk1YzZmMDgwZjgyYjlhNThmOTFkMDkyYmFlYTQ2N2Q3NDg5MjRmIiwidGFnIjoiIn0%3D; expires=Tue, 10 Jun 2025 09:39:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImEvUDRYWm90OVRpaGF3aVY2U0I0YUE9PSIsInZhbHVlIjoiMS9lSTNyRjdPSEhTR1Qva2lVMDdPaHgwZm5Vblp6aWJNQnZJNVBZKzFoelR1MlovL0xMdWRCbjcxUzBxQUFCN3lYSzc2c3lzaUJZdWxyNFpNdEFQUlJTcGFHZHpJMWRVTGJPWGxsK0YxdC9wWmdoNVVjZVBMR0lGNHNxQ0RLU1YiLCJtYWMiOiI3ZWIzZTU5M2ZlZWVhYWQ5ZWQyMGY0ZWU3YWFlNzEyY2RkMzgxYzBjYjY1YjQxMjQ2NDFlM2VkYzMyMDdmMTYwIiwidGFnIjoiIn0%3D; expires=Tue, 10 Jun 2025 09:39:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImdlbmdyQXdqTlVVY1FzejNJUnJ6SWc9PSIsInZhbHVlIjoiMStnVFBTbWtJaWdxTUFqOTkxMUZXaVBxZmM3L0dHSnlhVnlWaWFIRkhpSElUZkl4UGF1bk50MWlJdXpqMVdZQnJRSjZiMW9GUitDYytCZUdHNEVENUtGMTZTY2pBTEYveG9oelJvTVdOaWJZUklaT1Jsa1p3b2RkajVwS1hFVFMiLCJtYWMiOiJmNWUwZWQ1Zjk3YjIyYzc0Njk5ZGQwMTY4Zjk1YzZmMDgwZjgyYjlhNThmOTFkMDkyYmFlYTQ2N2Q3NDg5MjRmIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 09:39:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImEvUDRYWm90OVRpaGF3aVY2U0I0YUE9PSIsInZhbHVlIjoiMS9lSTNyRjdPSEhTR1Qva2lVMDdPaHgwZm5Vblp6aWJNQnZJNVBZKzFoelR1MlovL0xMdWRCbjcxUzBxQUFCN3lYSzc2c3lzaUJZdWxyNFpNdEFQUlJTcGFHZHpJMWRVTGJPWGxsK0YxdC9wWmdoNVVjZVBMR0lGNHNxQ0RLU1YiLCJtYWMiOiI3ZWIzZTU5M2ZlZWVhYWQ5ZWQyMGY0ZWU3YWFlNzEyY2RkMzgxYzBjYjY1YjQxMjQ2NDFlM2VkYzMyMDdmMTYwIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 09:39:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335972128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ExXS91dQAdl9KwlKTvODgBdfd3lNohUqjmvGjEj5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}