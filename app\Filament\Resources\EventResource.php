<?php

namespace App\Filament\Resources;

use App\Enums\EventCategoryType;
use App\Enums\RestrictionType;
use App\Filament\Resources\EventResource\Pages;
use App\Models\Event;
use App\Models\Language;
use App\Models\Restriction;
use App\Models\StadiumSector;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Tapp\FilamentTimezoneField\Forms\Components\TimezoneSelect;

class EventResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Event::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->get();

        return $form
            ->schema([
                Tabs::make('Event Creation')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Event Details')
                                    ->description('Enter the basic event information')
                                    ->schema([

                                        Tabs::make('content_tabs')
                                            ->columnSpanFull() // Ensure full width
                                            ->tabs(
                                                collect($languages)->map(function ($language, $key) {
                                                    return Tabs\Tab::make($language->name)
                                                        ->schema([
                                                            Hidden::make("translations.$key.locale")
                                                                ->default($language->locale),
                                                            TextInput::make("translations.$key.name")
                                                                ->label('Name')
                                                                ->required()
                                                                ->maxLength(255)
                                                                ->rules(function ($get, $record) use ($language) {
                                                                    return [
                                                                        Rule::unique(self::getEventTransTable(), 'name')
                                                                            ->where('locale', $language->locale)
                                                                            ->ignore($record?->id, 'event_id'),
                                                                    ];
                                                                }),

                                                            TextInput::make("slugs.$language->locale")
                                                                ->label('Slug')
                                                                ->required()
                                                                ->maxLength(255)
                                                                ->live(onBlur: true)
                                                                ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                                    $set("slugs.{$language->locale}", Str::slug($state));
                                                                })
                                                                ->rules(function ($get, $record) use ($language) {
                                                                    return [
                                                                        Rule::unique(self::getSlugTable(), 'slug')
                                                                            ->where(function ($query) use ($language, $record) {
                                                                                $query->where('locale', $language->locale);

                                                                                if ($record) {
                                                                                    $query->where(function ($q) use ($record) {
                                                                                        $q->where('sluggable_id', '!=', $record->id)
                                                                                            ->orWhere('sluggable_type', '!=', get_class($record));
                                                                                    });
                                                                                }
                                                                            }),
                                                                    ];
                                                                }),
                                                            Textarea::make("translations.$key.description")
                                                                ->label('Description')
                                                                ->required(),
                                                            TextInput::make("translations.$key.meta_title")
                                                                ->label('Meta Title')
                                                                ->maxLength(255)
                                                                ->required(),
                                                            TextInput::make("translations.$key.meta_keywords")
                                                                ->label('Meta Keywords')
                                                                ->maxLength(255)
                                                                ->required(),
                                                            Textarea::make("translations.$key.meta_description")
                                                                ->label('Meta Description')
                                                                ->maxLength(255)
                                                                ->required(),
                                                        ]);
                                                })->toArray()
                                            ),
                                        SpatieMediaLibraryFileUpload::make('image')
                                            ->required()
                                            ->disk('admin')
                                            ->columnSpan(2)
                                            ->customProperties(fn (Get $get): array => [
                                                'alt' => $get('image_alt'),
                                            ]),
                                        TextInput::make('image_alt')
                                            ->label('Image Alt Text')
                                            ->required()
                                            ->maxLength(255)
                                            ->columnSpanFull(),
                                        Grid::make(2)
                                            ->schema([
                                                DatePicker::make('date')
                                                    ->closeOnDateSelection()
                                                    ->native(false)
                                                    ->required(),
                                                TimePicker::make('time')
                                                    ->native(false)
                                                    ->seconds(false)
                                                    ->required(),
                                            ]),
                                        TimezoneSelect::make('timezone')
                                            ->timezoneType('GMT')
                                            ->searchable()
                                            ->required(),
                                        Select::make('category')
                                            ->required()
                                            ->options(EventCategoryType::getOptionsWithKeyValuePair()),
                                        Checkbox::make('is_feature_event')
                                            ->required()
                                            ->inline(false),
                                        Toggle::make('is_published')
                                            ->required()
                                            ->inline(false),
                                    ])->columns(2),

                                Section::make('Location Information')
                                    ->schema([
                                        Select::make('country_id')
                                            ->label('Country')
                                            ->required()
                                            ->relationship(
                                                name: 'country',
                                                titleAttribute: self::getCountryTransTable().'.name',
                                                modifyQueryUsing: function ($query) {
                                                    return $query->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
                                                        ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                                                }
                                            )
                                            ->preload()
                                            ->live()
                                            ->searchable()
                                            ->columnSpan(2),
                                        Select::make('stadium_id')
                                            ->disabled(fn (Get $get): bool => ! filled($get('country_id')))
                                            ->relationship(
                                                name: 'stadium',
                                                titleAttribute: self::getStadiumTransTable().'.name',
                                                modifyQueryUsing: function ($query, Get $get) {
                                                    $country_id = $get('country_id');

                                                    return $query->leftJoin(self::getStadiumTransTable(), self::getStadiumTable().'.id', '=', self::getStadiumTransTable().'.stadium_id')
                                                        ->where('country_id', $country_id)
                                                        ->where(self::getStadiumTransTable().'.locale', app()->getLocale());
                                                }
                                            )
                                            ->preload()
                                            ->live()
                                            ->searchable()
                                            ->required()
                                            ->columnSpan(2),
                                    ])->columns(2),
                            ]),

                        Tabs\Tab::make('Clubs & League')
                            ->schema([
                                Section::make('Clubs & League Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Select::make('home_club_id')
                                                    ->label('Home Club')
                                                    ->disabled(fn (Get $get): bool => ! filled($get('country_id')))
                                                    ->relationship(
                                                        name: 'homeClub',
                                                        titleAttribute: self::getClubTransTable().'.name',
                                                        modifyQueryUsing: function ($query, Get $get) {
                                                            $country_id = $get('country_id');

                                                            return $query->leftJoin(self::getClubTransTable(), self::getClubTable().'.id', '=', self::getClubTransTable().'.club_id')
                                                                ->where('country_id', $country_id)
                                                                ->where(self::getClubTransTable().'.locale', app()->getLocale());
                                                        }
                                                    )
                                                    ->preload()
                                                    ->live()
                                                    ->searchable()
                                                    ->required()
                                                    ->rule(function (Get $get) {
                                                        return function ($attribute, $value, $fail) use ($get) {
                                                            if ($value === $get('guest_club_id')) {
                                                                $fail('Home Club must be different from Guest Club.');
                                                            }
                                                        };
                                                    }),
                                                Select::make('guest_club_id')
                                                    ->label('Guest Club')
                                                    ->disabled(fn (Get $get): bool => ! filled($get('country_id')))
                                                    ->relationship(
                                                        name: 'guestClub',
                                                        titleAttribute: self::getClubTransTable().'.name',
                                                        modifyQueryUsing: function ($query, Get $get) {
                                                            $country_id = $get('country_id');

                                                            return $query->leftJoin(self::getClubTransTable(), self::getClubTable().'.id', '=', self::getClubTransTable().'.club_id')
                                                                ->where('country_id', $country_id)
                                                                ->where(self::getClubTransTable().'.locale', app()->getLocale());
                                                        }
                                                    )
                                                    ->preload()
                                                    ->live()
                                                    ->searchable()
                                                    ->required()
                                                    ->rule(function (Get $get) {
                                                        return function ($attribute, $value, $fail) use ($get) {
                                                            if ($value === $get('home_club_id')) {
                                                                $fail('Guest Club must be different from Home Club.');
                                                            }
                                                        };
                                                    }),
                                            ]),
                                        Select::make('league_id')
                                            ->disabled(fn (Get $get): bool => ! filled($get('country_id')))
                                            ->relationship(
                                                name: 'league',
                                                titleAttribute: self::getLeagueTransTable().'.name',
                                                modifyQueryUsing: function ($query, Get $get) {
                                                    $country_id = $get('country_id');

                                                    return $query->where('is_published', true)
                                                        ->leftJoin(self::getLeagueTransTable(), self::getLeagueTable().'.id', '=', self::getLeagueTransTable().'.league_id')
                                                        ->where('country_id', $country_id)
                                                        ->where(self::getLeagueTransTable().'.locale', app()->getLocale());
                                                }
                                            )
                                            ->live()
                                            ->searchable()
                                            ->required()
                                            ->preload()
                                            ->columnSpan(2),
                                    ])->columns(2),
                            ]),

                        Tabs\Tab::make('Sectors & Restrictions')
                            ->schema([
                                Section::make('Seating & Access')
                                    ->schema([
                                        CheckboxList::make('stadiumSectors')
                                            ->relationship('stadiumSectors', self::getStadiumSectorTransTable().'.name')
                                            ->disabled(fn (Get $get): bool => ! filled($get('stadium_id')))
                                            ->options(fn (Get $get) => StadiumSector::where('stadium_id', $get('stadium_id'))
                                                ->leftJoin(self::getStadiumSectorTransTable(), self::getStadiumSectorTable().'.id', '=', self::getStadiumSectorTransTable().'.stadium_sector_id')
                                                ->where(self::getStadiumSectorTransTable().'.locale', app()->getLocale())
                                                ->pluck(self::getStadiumSectorTransTable().'.name', self::getStadiumSectorTransTable().'.stadium_sector_id'))
                                            ->required()
                                            ->searchable()
                                            ->columns(3)
                                            ->gridDirection('row')
                                            ->extraAttributes([
                                                'style' => 'max-height: 300px !important; overflow-y: scroll !important;',
                                            ])
                                            ->bulkToggleable()
                                            ->columnSpanFull(),

                                        CheckboxList::make('restrictions')
                                            ->relationship('restrictions', self::getRestrictionTransTable().'.name')
                                            ->options(fn (Get $get) => Restriction::where('type', RestrictionType::EVENT->value)
                                                ->leftJoin(self::getRestrictionTransTable(), self::getRestrictionTable().'.id', '=', self::getRestrictionTransTable().'.restriction_id')
                                                ->where(self::getRestrictionTransTable().'.locale', app()->getLocale())
                                                ->pluck(self::getRestrictionTransTable().'.name', self::getRestrictionTransTable().'.restriction_id'))
                                            ->searchable()
                                            ->columns(2)
                                            ->required()
                                            ->gridDirection('row')
                                            ->extraAttributes([
                                                'style' => 'max-height: 300px !important; overflow-y: scroll !important;',
                                            ])
                                            ->bulkToggleable()
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('event_name')
                    ->label('Name')
                    ->searchable('et.name')
                    ->sortable(),
                TextColumn::make('category')
                    ->formatStateUsing(function (EventCategoryType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->searchable(),
                TextColumn::make('date')
                    ->date()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('time')
                    ->time()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('country_name')
                    ->sortable()
                    ->searchable('ct.name'),
                TextColumn::make('home_club_name')
                    ->label('Home Club')
                    ->sortable()
                    ->searchable('hct.name'),
                TextColumn::make('guest_club_name')
                    ->label('Guest Club')
                    ->sortable()
                    ->searchable('gct.name'),
                CheckboxColumn::make('is_feature_event'),
                TextColumn::make('stadium_name')
                    ->label('Stadium')
                    ->sortable()
                    ->searchable('st.name')
                    ->numeric(),
                TextColumn::make('league_name')
                    ->label('Leauge')
                    ->sortable()
                    ->searchable('lt.name')
                    ->numeric(),
                IconColumn::make('is_published')
                    ->boolean(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations;
                        $data['slugs'] = $record->slugs->pluck('slug', 'locale')->toArray();

                        $data['image_alt'] = optional($record->getFirstMedia())->getCustomProperty('alt');

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $slugs = $data['slugs'];
                        $translations = $data['translations'];
                        $sectors = $data['stadiumSectors'];
                        $image_alt = $data['image_alt'];

                        unset($data['slugs'], $data['translations'], $data['stadiumSectors'], $data['image_alt']);

                        // Update the main Event record
                        $record->update($data);

                        $record->setSlugs($slugs);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['event_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['event_id', 'locale'],
                            ['name', 'description', 'meta_title', 'meta_description', 'meta_keywords', 'updated_at']
                        );

                        $record->stadiumSectors()->sync($sectors);

                        $media = $record->getFirstMedia();
                        if ($media) {
                            $media->setCustomProperty('alt', $image_alt ?? '');
                            $media->save();
                        }

                        return $record;
                    })
                    ->visible(function (Event $record) {
                        return $record->league_published;
                    })
                    ->slideOver(),

                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->color('warning')
                    ->tooltip('Please publish the associated League to edit this record')
                    ->visible(function (Event $record) {
                        return ! $record->league_published;
                    }),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEvents::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getEventTable().'.*', 'et.name as event_name', 'lt.name as league_name', 'l.is_published as league_published', 'ct.name as country_name', 'st.name as stadium_name', 'hct.name as home_club_name', 'gct.name as guest_club_name')

            ->leftJoin(self::getEventTransTable().' as et', function ($join) {
                $join->on(self::getEventTable().'.id', '=', 'et.event_id')
                    ->where('et.locale', app()->getLocale());
            })
            ->leftJoin(self::getLeagueTable().' as l', function ($join) {
                $join->on(self::getEventTable().'.league_id', '=', 'l.id');
            })
            ->leftJoin(self::getLeagueTransTable().' as lt', function ($join) {
                $join->on(self::getEventTable().'.league_id', '=', 'lt.league_id')
                    ->where('lt.locale', app()->getLocale());
            })
            ->leftJoin(self::getCountryTransTable().' as ct', function ($join) {
                $join->on(self::getEventTable().'.country_id', '=', 'ct.country_id')
                    ->where('ct.locale', app()->getLocale());
            })
            ->leftJoin(self::getStadiumTransTable().' as st', function ($join) {
                $join->on(self::getEventTable().'.stadium_id', '=', 'st.stadium_id')
                    ->where('st.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as hct', function ($join) {
                $join->on(self::getEventTable().'.home_club_id', '=', 'hct.club_id')
                    ->where('hct.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as gct', function ($join) {
                $join->on(self::getEventTable().'.guest_club_id', '=', 'gct.club_id')
                    ->where('gct.locale', app()->getLocale());
            })
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
