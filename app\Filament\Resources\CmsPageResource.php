<?php

namespace App\Filament\Resources;

use AmidEsfahani\FilamentTinyEditor\TinyEditor;
use App\Filament\Resources\CmsPageResource\Pages;
use App\Models\CmsPage;
use App\Models\Language;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CmsPageResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = CmsPage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Content Management';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->get();

        return $form->schema([

            // Wrapping Tabs inside a Section for Full Width
            Section::make('Contents')
                ->schema([
                    Tabs::make('content_tabs')
                        ->columnSpanFull() // Ensure full width
                        ->tabs(
                            collect($languages)->map(function ($language, $key) {
                                return Tabs\Tab::make($language->name)
                                    ->schema([
                                        Hidden::make("translations.$key.locale")
                                            ->default($language->locale),
                                        TextInput::make("translations.$key.title")
                                            ->label('Title')
                                            ->required()
                                            ->maxLength(255)
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getCmsPageTransTable(), 'title')
                                                        ->where('locale', $language->locale)
                                                        ->ignore($record?->id, 'cms_page_id'),
                                                ];
                                            }),

                                        TextInput::make("slugs.$language->locale")
                                            ->label('Slug')
                                            ->required()
                                            ->maxLength(255)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                $set("slugs.{$language->locale}", Str::slug($state));
                                            })
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getSlugTable(), 'slug')
                                                        ->where(function ($query) use ($language, $record) {
                                                            $query->where('locale', $language->locale);

                                                            if ($record) {
                                                                $query->where(function ($q) use ($record) {
                                                                    $q->where('sluggable_id', '!=', $record->id)
                                                                        ->orWhere('sluggable_type', '!=', get_class($record));
                                                                });
                                                            }
                                                        }),
                                                ];
                                            }),

                                        TinyEditor::make("translations.$key.content")
                                            ->label('Content')
                                            ->setCustomConfigs([
                                                'valid_elements' => '*[*]',
                                            ])
                                            ->toolbarMode('wrap')
                                            ->required(),

                                        TextInput::make("translations.$key.meta_title")
                                            ->label('Meta Title')
                                            ->required()
                                            ->maxLength(255),

                                        TextInput::make("translations.$key.meta_description")
                                            ->label('Meta Description')
                                            ->required()
                                            ->maxLength(255),
                                    ]);
                            })->toArray()
                        ),
                ])
                ->columnSpanFull(), // Ensure full width for the section

            Toggle::make('is_active')
                ->required()
                ->inline(false),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Title')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations;

                        $data['slugs'] = $record->slugs->pluck('slug', 'locale')->toArray();

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $slugs = $data['slugs'];
                        $translations = $data['translations'];

                        unset($data['slugs'], $data['translations']);

                        // Update the main CMS Page record
                        $record->update($data);

                        $record->setSlugs($slugs);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['cms_page_id'] = $record->id;

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['cms_page_id', 'locale'],
                            ['title', 'content', 'meta_title', 'meta_description']
                        );

                        return $record;
                    })
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->slideOver(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCmsPages::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getCmsPageTable().'.*', self::getCmsPageTransTable().'.title', self::getSlugTable().'.slug')

            ->leftJoin(self::getCmsPageTransTable(), function ($join) {
                $join->on(self::getCmsPageTable().'.id', '=', self::getCmsPageTransTable().'.cms_page_id')
                    ->where(self::getCmsPageTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getSlugTable(), function ($join) {
                $join->on(self::getCmsPageTable().'.id', '=', self::getSlugTable().'.sluggable_id')
                    ->where(self::getSlugTable().'.sluggable_type', '=', CmsPage::class)
                    ->where(self::getSlugTable().'.locale', app()->getLocale());
            })
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
