<?php

namespace Database\Factories;

use App\Models\Country;
use App\Models\Language;
use App\Models\Season;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\League>
 */
class LeagueFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'season_id' => Season::inRandomOrder()->first(),
            'country_id' => Country::whereIn('shortcode', ['IT', 'ES'])->inRandomOrder()->first(),
            'is_published' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($league) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $slugs = [];
            $translationData = [];

            foreach ($languages as $value) {
                $name = $this->faker->name;
                $translationData[] = [
                    'league_id' => $league->id,
                    'locale' => $value,
                    'name' => $name,
                    'description' => $this->faker->paragraph,
                    'meta_title' => $this->faker->sentence,
                    'meta_description' => $this->faker->sentence,
                    'meta_keywords' => $this->faker->sentence,
                ];

                $slugs[$value] = Str::slug('league-'.$name.'-'.$league->id);
            }
            $league->translations()->createMany($translationData);
            $league->setSlugs($slugs);
        });
    }
}
