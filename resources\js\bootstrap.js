import axios from "axios";
window.axios = axios;
window.axios.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";

const htmlLang = document.documentElement.lang;
window.axios.defaults.headers.common["X-Locale"] = htmlLang;
axios.defaults.withCredentials = true;
axios.defaults.withXSRFToken = true;

axios.interceptors.response.use((response) => {
    if (response.data.success && response.data.data) {
        response.data = {
            ...response.data.data,
            success: response.data.success,
            message: response.data.message,
        };
        return response;
    }

    return response;
});
