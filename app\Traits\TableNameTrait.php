<?php

namespace App\Traits;

use App\Models\Club;
use App\Models\ClubTranslation;
use App\Models\CmsPage;
use App\Models\CmsPageTranslation;
use App\Models\Country;
use App\Models\CountryTranslation;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateTranslation;
use App\Models\Event;
use App\Models\EventTranslation;
use App\Models\League;
use App\Models\LeagueTranslation;
use App\Models\Order;
use App\Models\Restriction;
use App\Models\RestrictionTranslation;
use App\Models\Season;
use App\Models\SeasonTranslation;
use App\Models\Slug;
use App\Models\Stadium;
use App\Models\StadiumSector;
use App\Models\StadiumSectorTranslation;
use App\Models\StadiumTranslation;
use App\Models\Ticket;
use App\Models\TicketTranslation;

trait TableNameTrait
{
    protected static ?string $clubTable = null;

    protected static ?string $clubTransTable = null;

    protected static ?string $cmsPageTable = null;

    protected static ?string $cmsPageTransTable = null;

    protected static ?string $countryTable = null;

    protected static ?string $countryTransTable = null;

    protected static ?string $eventTable = null;

    protected static ?string $eventTransTable = null;

    protected static ?string $leagueTable = null;

    protected static ?string $leagueTransTable = null;

    protected static ?string $restrictionTable = null;

    protected static ?string $restrictionTransTable = null;

    protected static ?string $seasonTable = null;

    protected static ?string $seasonTransTable = null;

    protected static ?string $slugTable = null;

    protected static ?string $stadiumTable = null;

    protected static ?string $stadiumTransTable = null;

    protected static ?string $stadiumSectorTable = null;

    protected static ?string $stadiumSectorTransTable = null;

    protected static ?string $emailTemaplateTable = null;

    protected static ?string $emailTemplateTransTable = null;

    protected static ?string $ticketTable = null;

    protected static ?string $ticketTransTable = null;

    protected static ?string $orderTable = null;

    protected static function getClubTable(): string
    {
        return self::$clubTable ?? (new Club)->getTable();
    }

    protected static function getClubTransTable(): string
    {
        return self::$clubTransTable ?? (new ClubTranslation)->getTable();
    }

    protected static function getCmsPageTable(): string
    {
        return self::$cmsPageTable ?? (new CmsPage)->getTable();
    }

    protected static function getCmsPageTransTable(): string
    {
        return self::$cmsPageTransTable ?? (new CmsPageTranslation)->getTable();
    }

    protected static function getCountryTable(): string
    {
        return self::$countryTable ?? (new Country)->getTable();
    }

    protected static function getCountryTransTable(): string
    {
        return self::$countryTransTable ?? (new CountryTranslation)->getTable();
    }

    protected static function getEmailTemplateTable(): string
    {
        return self::$emailTemplateTable ?? (new EmailTemplate)->getTable();
    }

    protected static function getEmailTemplateTransTable(): string
    {
        return self::$emailTemplateTransTable ?? (new EmailTemplateTranslation)->getTable();
    }

    protected static function getEventTable(): string
    {
        return self::$eventTable ?? (new Event)->getTable();
    }

    protected static function getEventTransTable(): string
    {
        return self::$eventTransTable ?? (new EventTranslation)->getTable();
    }

    protected static function getLeagueTable(): string
    {
        return self::$leagueTable ?? (new League)->getTable();
    }

    protected static function getLeagueTransTable(): string
    {
        return self::$leagueTransTable ?? (new LeagueTranslation)->getTable();
    }

    protected static function getRestrictionTable(): string
    {
        return self::$restrictionTable ?? (new Restriction)->getTable();
    }

    protected static function getRestrictionTransTable(): string
    {
        return self::$restrictionTransTable ?? (new RestrictionTranslation)->getTable();
    }

    protected static function getSeasonTable(): string
    {
        return self::$seasonTable ?? (new Season)->getTable();
    }

    protected static function getSeasonTransTable(): string
    {
        return self::$seasonTransTable ?? (new SeasonTranslation)->getTable();
    }

    protected static function getSlugTable(): string
    {
        return self::$slugTable ?? (new Slug)->getTable();
    }

    protected static function getStadiumTable(): string
    {
        return self::$stadiumTable ?? (new Stadium)->getTable();
    }

    protected static function getStadiumTransTable(): string
    {
        return self::$stadiumTransTable ?? (new StadiumTranslation)->getTable();
    }

    protected static function getStadiumSectorTable(): string
    {
        return self::$stadiumSectorTable ?? (new StadiumSector)->getTable();
    }

    protected static function getStadiumSectorTransTable(): string
    {
        return self::$stadiumSectorTransTable ?? (new StadiumSectorTranslation)->getTable();
    }

    protected static function getTicketTable(): string
    {
        return self::$ticketTable ?? (new Ticket)->getTable();
    }

    protected static function getTicketTransTable(): string
    {
        return self::$ticketTransTable ?? (new TicketTranslation)->getTable();
    }

    protected static function getOrderTable(): string
    {
        return self::$orderTable ?? (new Order)->getTable();
    }
}
