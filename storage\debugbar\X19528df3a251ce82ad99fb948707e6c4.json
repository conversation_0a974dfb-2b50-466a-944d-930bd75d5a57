{"__meta": {"id": "X19528df3a251ce82ad99fb948707e6c4", "datetime": "2025-06-11 05:37:35", "utime": **********.177045, "method": "GET", "uri": "/api/v1/leagues/filters", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749620254.99964, "end": **********.177058, "duration": 0.17741799354553223, "duration_str": "177ms", "measures": [{"label": "Booting", "start": 1749620254.99964, "relative_start": 0, "end": **********.052585, "relative_end": **********.052585, "duration": 0.05294489860534668, "duration_str": "52.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.0526, "relative_start": 0.05295991897583008, "end": **********.177061, "relative_end": 3.0994415283203125e-06, "duration": 0.12446117401123047, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5908464, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/leagues/filters", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\LeagueController@getFilters", "namespace": null, "where": [], "as": "api.leagues.filters", "prefix": "api/v1/leagues", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLeagueController.php&line=54\" onclick=\"\">app/Http/Controllers/Api/V1/LeagueController.php:54-69</a>"}, "queries": {"nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023710000000000002, "accumulated_duration_str": "23.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u' limit 1", "type": "query", "params": [], "bindings": ["0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.057599, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.91}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.061625, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 2.91, "width_percent": 3.29}, {"sql": "select `id` from `countries` where `is_published` = 1 and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/LeagueController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\LeagueController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.124927, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 6.2, "width_percent": 5.23}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/LeagueController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\LeagueController.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.134903, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 11.43, "width_percent": 5.483}, {"sql": "select `id` from `seasons` where `is_published` = 1 and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Services/SeasonService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\SeasonService.php", "line": 37}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/LeagueController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\LeagueController.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.145874, "duration": 0.009779999999999999, "duration_str": "9.78ms", "memory": 0, "memory_str": null, "filename": "SeasonRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FSeasonRepository.php&line=29", "ajax": false, "filename": "SeasonRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 16.913, "width_percent": 41.248}, {"sql": "select `season_id`, `name` from `season_translations` where `locale` = 'en' and `season_translations`.`season_id` in (2, 3, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/SeasonService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\SeasonService.php", "line": 37}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/LeagueController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\LeagueController.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.160814, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "SeasonRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FSeasonRepository.php&line=29", "ajax": false, "filename": "SeasonRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 58.161, "width_percent": 31.59}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (2, 3, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Season'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Season"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/SeasonService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\SeasonService.php", "line": 37}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/LeagueController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\LeagueController.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.170547, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "SeasonRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/SeasonRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\SeasonRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FSeasonRepository.php&line=29", "ajax": false, "filename": "SeasonRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 89.751, "width_percent": 6.116}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiblZ3dWRYYnljYzhYV1I2UGRzdWx6M21OZXVHd2hVVEZrWktFeE1DTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkT1hoMTBrSUVReGpaUDIzNVFRVUZpZXJxVi91UGQvTjZ0RE81YkFobDlXUHM2VFZ5anNqdVMiO30=', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = '0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiblZ3dWRYYnljYzhYV1I2UGRzdWx6M21OZXVHd2hVVEZrWktFeE1DTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkT1hoMTBrSUVReGpaUDIzNVFRVUZpZXJxVi91UGQvTjZ0RE81YkFobDlXUHM2VFZ5anNqdVMiO30=", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.175091, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 95.867, "width_percent": 4.133}]}, "models": {"data": {"App\\Models\\Country": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Season": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSeason.php&line=1", "ajax": false, "filename": "Season.php", "line": "?"}}, "App\\Models\\SeasonTranslation": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSeasonTranslation.php&line=1", "ajax": false, "filename": "SeasonTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 520, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/events\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$OXh10kIEQxjZP235QQUFierqV/uPd/N6tDO5bAhl9WPs6TVyjsjuS"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f208561-5348-4cf9-831c-665ebaca89f7\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/leagues/filters", "status_code": "<pre class=sf-dump id=sf-dump-1956006068 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1956006068\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-69914887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-69914887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2085701546 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2085701546\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2131773448 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IjEyNkJUSUxlUXN1YWJmMksxQmZZc1E9PSIsInZhbHVlIjoiOVI0VWtNaGZTVnJLQnRIRERwa3QwMTFBRHR5UmJRYzgwcnFXemgzc1JJK3p1YVlvRWtRaFBjNkNGYmlwOFZJUUNJaUhFMnNMMUNJT0JGZkpnNWtLTmduTGprS2Y3elJ6WEd3azFLcW1lVXh1OExVeGRCZmFUMXlZWVNFU0JwZjMiLCJtYWMiOiI0ODY5OTYyODI1YmIwYTllNzU0OWQxYThmZTUwODRmYjBjNThkZjNkODhmMzkyMjNkODNhOGQzMmRiNDAxZmVmIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkppNGNJTzdsZm12VXd3SWVTYk9vNEE9PSIsInZhbHVlIjoiUWwydFJ3NU84VFViRnBsWjgrNEVjdjhkd3czSnMxNkhEU1cwM0VSa1BZLzk5VFpsUGpZNy9TaCt1T2MyNXRZdUc4QzM1dDlEQkJhOFdVYUVxcnZPMVVGL2s2SW1KeURUTGxUZ1NEZkRnY1Z0SFFkYjhlc2RjMnVnRG5KZzdNOWwiLCJtYWMiOiI1MTU3N2RjYTkzZjYzOTc5NjhmNzBlNGE4YzNiMDU1MTQ2NDRhM2U4OWRlMDRlMjQyMWQ2ZDczZGM0YzIyZGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://ticketgol.test/leagues</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjEyNkJUSUxlUXN1YWJmMksxQmZZc1E9PSIsInZhbHVlIjoiOVI0VWtNaGZTVnJLQnRIRERwa3QwMTFBRHR5UmJRYzgwcnFXemgzc1JJK3p1YVlvRWtRaFBjNkNGYmlwOFZJUUNJaUhFMnNMMUNJT0JGZkpnNWtLTmduTGprS2Y3elJ6WEd3azFLcW1lVXh1OExVeGRCZmFUMXlZWVNFU0JwZjMiLCJtYWMiOiI0ODY5OTYyODI1YmIwYTllNzU0OWQxYThmZTUwODRmYjBjNThkZjNkODhmMzkyMjNkODNhOGQzMmRiNDAxZmVmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131773448\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-651645349 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0MdKbS7RFR29JRkKfiTeEKCw3dOzz4J8miVRPB0u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651645349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-111083907 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 05:37:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9ka3QwMTNJL29sTExnazdLejVwTWc9PSIsInZhbHVlIjoiWTJuTVFSc0sxOFk1ajFFSG9vTGJCTmt3QjdkVnYxQkFoUUMvempsK0FKUmt6dmo0bm52eDk3VDNoaGgyOXIrWVdWZGRYdlpJSzltSjcwWkhYNVZDZXdhWU9aajlJMjFKbTUwYXhuZ1NUUlJ3eHRnOXNidDhSTFluS2xPQktjbUIiLCJtYWMiOiJlMDAwN2E3ZGFjN2E2NjQwNDNmNWY3ZTllNTYzNGZhZTM2Y2I5N2FhNmU2NTg2ZWNmMzljY2VhMzNlZjJmNzgxIiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 07:37:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Im1jaGZ1aHYrL3dtNkxUMWhVMjRraUE9PSIsInZhbHVlIjoic1QzbjJram5uVklDN0kwVGlFOWpMcnpBVGNEU3pBaFU4Ty84ZGRQK3JBa01Qcmo2TUI5eDN6WVBkYnplbzZOd2hMZDVXWkptOGl5MXZMUnhhaDFQeUhIcnMrQmQvcW5hak5VSGZlcldQelV4RnhnV0xVUW8xZnA3V1ByWHhNUTUiLCJtYWMiOiI0ZDlkNDFhNzUxNmEyMjcwNWNiMzlmNmIyMmJmYWIxNzQwYWI1MWVhMTUwYzY2NDBiNTdjNjUwYThiODJhZDM2IiwidGFnIjoiIn0%3D; expires=Wed, 11 Jun 2025 07:37:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9ka3QwMTNJL29sTExnazdLejVwTWc9PSIsInZhbHVlIjoiWTJuTVFSc0sxOFk1ajFFSG9vTGJCTmt3QjdkVnYxQkFoUUMvempsK0FKUmt6dmo0bm52eDk3VDNoaGgyOXIrWVdWZGRYdlpJSzltSjcwWkhYNVZDZXdhWU9aajlJMjFKbTUwYXhuZ1NUUlJ3eHRnOXNidDhSTFluS2xPQktjbUIiLCJtYWMiOiJlMDAwN2E3ZGFjN2E2NjQwNDNmNWY3ZTllNTYzNGZhZTM2Y2I5N2FhNmU2NTg2ZWNmMzljY2VhMzNlZjJmNzgxIiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 07:37:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Im1jaGZ1aHYrL3dtNkxUMWhVMjRraUE9PSIsInZhbHVlIjoic1QzbjJram5uVklDN0kwVGlFOWpMcnpBVGNEU3pBaFU4Ty84ZGRQK3JBa01Qcmo2TUI5eDN6WVBkYnplbzZOd2hMZDVXWkptOGl5MXZMUnhhaDFQeUhIcnMrQmQvcW5hak5VSGZlcldQelV4RnhnV0xVUW8xZnA3V1ByWHhNUTUiLCJtYWMiOiI0ZDlkNDFhNzUxNmEyMjcwNWNiMzlmNmIyMmJmYWIxNzQwYWI1MWVhMTUwYzY2NDBiNTdjNjUwYThiODJhZDM2IiwidGFnIjoiIn0%3D; expires=Wed, 11-Jun-2025 07:37:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111083907\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-828492920 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nVwudXbycc8XWR6Pdsulz3mNeuGwhUTFkZKExMCM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$OXh10kIEQxjZP235QQUFierqV/uPd/N6tDO5bAhl9WPs6TVyjsjuS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828492920\", {\"maxDepth\":0})</script>\n"}}