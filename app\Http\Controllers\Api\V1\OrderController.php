<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\OrderStatus;
use App\Helpers\ApiResponse;
use App\Services\OrderService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\Api\V1\OrderFilterRequest;

class OrderController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function index(OrderFilterRequest $request)
    {
        $filtersDTO = $request->toDTO();
        $orders = $this->orderService->getPaginatedOrdersForUser(Auth::id(), $filtersDTO);

        return ApiResponse::success($orders, 'SUCCESS');
    }

    public function getStatuses()
    {
        $statuses = $this->orderService->getStatusOptions();

        return ApiResponse::success(['statuses' => $statuses], 'SUCCESS');
    }
}
