<?php

namespace App\Filament\Resources\StadiumResource\Pages;

use App\Filament\Resources\StadiumResource;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditStadium extends EditRecord
{
    protected static string $resource = StadiumResource::class;

    public function getHeading(): string
    {
        return $this->record->translation->name;
    }

    /**
     * Pre-fill the form with existing translations
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Ensure the form structure includes translations
        $data['translations'] = $this->record->translations;

        $data['slugs'] = $this->record->slugs->pluck('slug', 'locale')->toArray();

        $data['image_alt'] = optional($this->record->getFirstMedia())->getCustomProperty('alt');

        return $data;
    }

    /**
     * Handle record update and save translations
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $slugs = $data['slugs'];
        $translations = $data['translations'];
        $image_alt = $data['image_alt'];

        unset($data['slugs'], $data['translations'], $data['image_alt']);

        // Update the main season record
        $record->update($data);

        $record->setSlugs($slugs);

        $translations = collect($translations)->map(function ($translation) use ($record) {
            $translation['stadium_id'] = $record->id;
            $translation['updated_at'] = now();

            return $translation;
        })->toArray();

        $record->translations()->upsert(
            $translations,
            ['stadium_id', 'locale'],
            ['name', 'description', 'meta_title', 'meta_keywords', 'meta_description', 'updated_at']
        );

        $media = $record->getFirstMedia();
        if ($media) {
            $media->setCustomProperty('alt', $image_alt ?? '');
            $media->save();
        }

        return $record;
    }
}
