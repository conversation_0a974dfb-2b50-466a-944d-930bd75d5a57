<?php

namespace App\Http\Controllers;

use App\Enums\EventCategoryType;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class EventController extends Controller
{
    /**
     * Display the Events listings
     */
    public function index(Request $request): Response
    {
        return Inertia::render('Events/Index', [
            'categories' => EventCategoryType::getOptionsWithKeyValuePair(),
        ]);
    }
}
