<?php

namespace App\Filament\Resources\CmsPageResource\Pages;

use App\Filament\Resources\CmsPageResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageCmsPages extends ManageRecords
{
    protected static string $resource = CmsPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $slugs = $data['slugs'];
                    $translations = $data['translations'];

                    unset($data['slugs'], $data['translations']);

                    // Save CMS Page
                    $cmsPage = static::getModel()::create($data);

                    $cmsPage->setSlugs($slugs);
                    $cmsPage->translations()->createMany($translations);

                    return $cmsPage;
                })
                ->closeModalByEscaping(false)
                ->closeModalByClickingAway(false)
                ->slideOver(),
        ];
    }
}
