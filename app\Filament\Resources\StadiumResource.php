<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StadiumResource\Pages;
use App\Filament\Resources\StadiumResource\RelationManagers\SectorsRelationManager;
use App\Models\Language;
use App\Models\Stadium;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class StadiumResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Stadium::class;

    protected static ?string $pluralModelLabel = 'Stadiums';

    protected static ?string $slug = 'stadiums';

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->get();

        return $form
            ->schema([

                Section::make('Contents')
                    ->schema([
                        Tabs::make('content_tabs')
                            ->columnSpanFull() // Ensure full width
                            ->tabs(
                                collect($languages)->map(function ($language, $key) {
                                    return Tabs\Tab::make($language->name)
                                        ->schema([
                                            Hidden::make("translations.$key.locale")
                                                ->default($language->locale),
                                            TextInput::make("translations.$key.name")
                                                ->label('Name')
                                                ->required()
                                                ->maxLength(255)
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getStadiumTransTable(), 'name')
                                                            ->where('locale', $language->locale)
                                                            ->ignore($record?->id, 'stadium_id'),
                                                    ];
                                                }),

                                            TextInput::make("slugs.$language->locale")
                                                ->label('Slug')
                                                ->required()
                                                ->maxLength(255)
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                    $set("slugs.{$language->locale}", Str::slug($state));
                                                })
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getSlugTable(), 'slug')
                                                            ->where(function ($query) use ($language, $record) {
                                                                $query->where('locale', $language->locale);

                                                                if ($record) {
                                                                    $query->where(function ($q) use ($record) {
                                                                        $q->where('sluggable_id', '!=', $record->id)
                                                                            ->orWhere('sluggable_type', '!=', get_class($record));
                                                                    });
                                                                }
                                                            }),
                                                    ];
                                                }),
                                            Textarea::make("translations.$key.description")
                                                ->required()
                                                ->maxLength(255)
                                                ->label('Description'),
                                            TextInput::make("translations.$key.meta_title")
                                                ->label('Meta Title')
                                                ->maxLength(255)
                                                ->required(),
                                            TextInput::make("translations.$key.meta_keywords")
                                                ->label('Meta Keywords')
                                                ->maxLength(255)
                                                ->required(),
                                            Textarea::make("translations.$key.meta_description")
                                                ->label('Meta Description')
                                                ->maxLength(255)
                                                ->required(),
                                        ]);
                                })->toArray()
                            ),
                    ])
                    ->columnSpanFull(), // Ensure full width for the section
                TextInput::make('address_line_1')
                    ->required()
                    ->maxLength(255),
                TextInput::make(name: 'address_line_2')
                    ->required()
                    ->maxLength(255),
                TextInput::make('postcode')
                    ->required()
                    ->maxLength(20),
                Select::make('country_id')
                    ->label('Country')
                    ->required()
                    ->relationship('country', self::getCountryTransTable().'.name', function ($query) {
                        return $query->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
                            ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                    })
                    ->preload()
                    ->searchable(),
                Toggle::make('is_published')
                    ->required()
                    ->inline(false),
                SpatieMediaLibraryFileUpload::make('image')
                    ->required()
                    ->columnSpanFull()
                    ->disk('admin')
                    ->customProperties(fn (Get $get): array => [
                        'alt' => $get('image_alt'),
                    ]),
                TextInput::make('image_alt')
                    ->label('Image Alt Text')
                    ->required()
                    ->maxLength(255)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                BadgeableColumn::make('stadium_name')
                    ->label('Name')
                    ->suffixBadges(function ($record) {
                        return [
                            Badge::make($record->sectors_count)
                                ->label("sectors:$record->sectors_count")
                                ->color('success'),
                        ];
                    })
                    ->separator('-')
                    ->sortable()
                    ->searchable(self::getStadiumTransTable().'.name'),
                Tables\Columns\TextColumn::make('address_line_1')
                    ->sortable()
                    ->limit(30)
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_line_2')
                    ->sortable()
                    ->limit(30)
                    ->searchable(),
                Tables\Columns\TextColumn::make('postcode')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('country_name')
                    ->sortable()
                    ->searchable(self::getCountryTransTable().'.name'),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_published')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $query->withCount('sectors');
            })
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageStadiums::route('/'),
            'edit' => Pages\EditStadium::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (! request()->routeIs('filament.admin.resources.stadiums.edit')) {
            $query->select(self::getStadiumTable().'.*', self::getStadiumTransTable().'.name as stadium_name', self::getCountryTransTable().'.name as country_name', self::getSlugTable().'.slug')
                ->leftJoin(self::getStadiumTransTable(), function ($join) {
                    $join->on(self::getStadiumTable().'.id', '=', self::getStadiumTransTable().'.stadium_id')
                        ->where(self::getStadiumTransTable().'.locale', app()->getLocale());
                })
                ->leftJoin(self::getCountryTransTable(), function ($join) {
                    $join->on(self::getStadiumTable().'.country_id', '=', self::getCountryTransTable().'.country_id')
                        ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                })
                ->leftJoin(self::getSlugTable(), function ($join) {
                    $join->on(self::getStadiumTable().'.id', '=', self::getSlugTable().'.sluggable_id')
                        ->where(self::getSlugTable().'.sluggable_type', '=', Stadium::class)
                        ->where(self::getSlugTable().'.locale', app()->getLocale());
                });
        }

        return $query->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    public static function getRelations(): array
    {
        return [
            SectorsRelationManager::class,
        ];
    }
}
