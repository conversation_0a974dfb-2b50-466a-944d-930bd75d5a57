<?php

namespace App\Models;

use App\Traits\CascadeSoftDeletes;
use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Season extends Model
{
    /** @use HasFactory<\Database\Factories\SeasonFactory> */
    use CascadeSoftDeletes, CustomActivityLog, HasFactory, HasSlugs, SoftDeletes;

    protected $fillable = ['is_published'];

    // Define relationships that should be cascade soft deleted
    protected $cascadeDeletes = ['leauges'];

    public function leauges()
    {
        return $this->hasMany(League::class, 'season_id', 'id');
    }

    public function translations()
    {
        return $this->hasMany(SeasonTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(SeasonTranslation::class, 'season_id', 'id')
            ->where('locale', app()->getLocale());
    }
}
