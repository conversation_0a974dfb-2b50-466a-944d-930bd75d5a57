<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class OrderFilterDTO implements DTOInterface
{
    public function __construct(
        public ?string $search = '',
        public ?string $status = '',
        public ?string $dateFrom = '',
        public ?string $dateTo = '',
    ) {}

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'status' => $this->status,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
        ];
    }
}
