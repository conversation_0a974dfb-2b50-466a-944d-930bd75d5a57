<?php

namespace App\Filament\Resources\StadiumResource\Pages;

use App\Filament\Resources\StadiumResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageStadiums extends ManageRecords
{
    protected static string $resource = StadiumResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $translations = $data['translations'];
                    $slugs = $data['slugs'];

                    unset($data['slugs'], $data['translations'], $data['image_alt']);

                    // Save stadium and translations
                    $stadium = static::getModel()::create($data);

                    $stadium->setSlugs($slugs);

                    $stadium->translations()->createMany($translations);

                    return $stadium;
                }),
        ];
    }
}
