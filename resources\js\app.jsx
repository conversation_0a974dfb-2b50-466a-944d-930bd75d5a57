import "../css/app.css";
import "./bootstrap";

import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { createRoot } from "react-dom/client";
import { store } from "./redux/store";
import { Provider } from "react-redux";
import * as Sentry from "@sentry/react";
import { StrictMode } from "react";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_REACT_DSN,
    sendDefaultPii: true,
    integrations: [Sentry.replayIntegration()],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
});

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.jsx`,
            import.meta.glob("./Pages/**/*.jsx"),
        ),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <StrictMode>
                <Provider store={store}>
                    <App {...props} />
                </Provider>
            </StrictMode>,
        );
    },
    progress: {
        color: "#4B5563",
    },
});
